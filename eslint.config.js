import { defineConfig } from "eslint/config";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  {
    ignores: [
      ".cache",
      ".react-router",
      "node_modules",
      "public/build",
      "public/datadog-worker*",
      "build",
      "app/api/openapi/generated",
    ],
  },
  {
    extends: compat.extends(
      "eslint:recommended",
      "plugin:react/recommended",
      "plugin:react-hooks/recommended-legacy",
      "plugin:@typescript-eslint/recommended"
    ),
    settings: {
      react: {
        version: "detect",
      },
    },
    rules: {
      "no-console": "error",
      "@typescript-eslint/consistent-type-imports": "off",
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      // Suppress comma-dangle errors
      "comma-dangle": "off",
      // Suppress TypeScript errors
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "@typescript-eslint/ban-ts-comment": "off",
      // Suppress React errors
      "react/no-unescaped-entities": "off",
      "react/display-name": "off",
      // Suppress JavaScript/general errors
      "no-case-declarations": "off",
      "no-useless-catch": "off",
      "prefer-rest-params": "off",
      "no-unsafe-optional-chaining": "off",
    },
  },
  {
    languageOptions: {
      globals: {
        console: "readonly",
        process: "readonly",
        require: "readonly",
      },
    },
  },
]);
