import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";

import { UiSettingsProvider, useUiSettings } from "./uiSettings";

// Test component that uses the UI settings context
const TestComponent = () => {
  const uiSettings = useUiSettings();
  return (
    <div>
      <div data-testid="theme">{uiSettings.theme}</div>
    </div>
  );
};

describe("UiSettingsProvider", () => {
  it("provides default UI settings", () => {
    const defaultSettings = { theme: "light" as const };

    render(
      <UiSettingsProvider uiSettings={defaultSettings}>
        <TestComponent />
      </UiSettingsProvider>
    );

    expect(screen.getByTestId("theme")).toHaveTextContent("light");
  });

  it("provides custom UI settings", () => {
    const customSettings = { theme: "dark" as const };

    render(
      <UiSettingsProvider uiSettings={customSettings}>
        <TestComponent />
      </UiSettingsProvider>
    );

    expect(screen.getByTestId("theme")).toHaveTextContent("dark");
  });

  it("allows accessing theme setting", () => {
    const settings = { theme: "dark" as const };

    render(
      <UiSettingsProvider uiSettings={settings}>
        <TestComponent />
      </UiSettingsProvider>
    );

    expect(screen.getByTestId("theme")).toHaveTextContent("dark");
  });
});
