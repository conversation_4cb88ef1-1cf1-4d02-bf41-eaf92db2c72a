import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { useContext } from "react";
import { Flags } from "./types";
import { FlagContext, FlagProvider, useFlag } from "./flags";

describe("FlagProvider", () => {
  it("provides flags context to children", () => {
    const flags: Flags = {
      flags: { featureA: { is_active: true } },
      switches: {},
      samples: {},
    };

    const TestingComponent = () => {
      const flags = useContext(FlagContext);
      return <div data-testid="flags">{JSON.stringify(flags)}</div>;
    };

    render(
      <FlagProvider flags={flags}>
        <TestingComponent />
      </FlagProvider>
    );

    expect(screen.getByTestId("flags")).toHaveTextContent(
      JSON.stringify(flags)
    );
  });
});

describe("useFlag", () => {
  it("returns true if the flag is active", () => {
    const flags: Flags = {
      flags: { featureA: { is_active: true } },
      switches: {},
      samples: {},
    };

    const TestComponent = () => {
      const isActive = useFlag("featureA");
      return <div>{isActive ? "Active" : "Inactive"}</div>;
    };

    render(
      <FlagProvider flags={flags}>
        <TestComponent />
      </FlagProvider>
    );

    expect(screen.getByText("Active")).toBeInTheDocument();
  });

  it("returns false if the flag is not active", () => {
    const flags: Flags = {
      flags: { featureA: { is_active: false } },
      switches: {},
      samples: {},
    };

    const TestComponent = () => {
      const isActive = useFlag("featureA");
      return <div>{isActive ? "Active" : "Inactive"}</div>;
    };

    render(
      <FlagProvider flags={flags}>
        <TestComponent />
      </FlagProvider>
    );

    expect(screen.getByText("Inactive")).toBeInTheDocument();
  });

  it("returns undefined if the flag does not exist", () => {
    const flags: Flags = {
      flags: {},
      switches: {},
      samples: {},
    };

    const TestComponent = () => {
      const isActive = useFlag("featureA");
      return <div>{isActive === undefined ? "Undefined" : "Defined"}</div>;
    };

    render(
      <FlagProvider flags={flags}>
        <TestComponent />
      </FlagProvider>
    );

    expect(screen.getByText("Undefined")).toBeInTheDocument();
  });
});
