/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Status } from './Status';
import {
    StatusFromJSON,
    StatusFromJSONTyped,
    StatusToJSON,
} from './Status';
import type { DeepinsightsCoreMetricsCustomerInsightsClient } from './DeepinsightsCoreMetricsCustomerInsightsClient';
import {
    DeepinsightsCoreMetricsCustomerInsightsClientFromJSON,
    DeepinsightsCoreMetricsCustomerInsightsClientFromJSONTyped,
    DeepinsightsCoreMetricsCustomerInsightsClientToJSON,
} from './DeepinsightsCoreMetricsCustomerInsightsClient';
import type { User } from './User';
import {
    User<PERSON>romJ<PERSON><PERSON>,
    User<PERSON>romJ<PERSON>NTyped,
    UserToJSON,
} from './User';
import type { StructuredMeetingDataInsightData } from './StructuredMeetingDataInsightData';
import {
    StructuredMeetingDataInsightDataFromJSON,
    StructuredMeetingDataInsightDataFromJSONTyped,
    StructuredMeetingDataInsightDataToJSON,
} from './StructuredMeetingDataInsightData';
import type { NoteInsightData } from './NoteInsightData';
import {
    NoteInsightDataFromJSON,
    NoteInsightDataFromJSONTyped,
    NoteInsightDataToJSON,
} from './NoteInsightData';
import type { ScheduledEventInsightData } from './ScheduledEventInsightData';
import {
    ScheduledEventInsightDataFromJSON,
    ScheduledEventInsightDataFromJSONTyped,
    ScheduledEventInsightDataToJSON,
} from './ScheduledEventInsightData';
import type { TaskInsightData } from './TaskInsightData';
import {
    TaskInsightDataFromJSON,
    TaskInsightDataFromJSONTyped,
    TaskInsightDataToJSON,
} from './TaskInsightData';

/**
 * 
 * @export
 * @interface InsightsDashboardResponse
 */
export interface InsightsDashboardResponse {
    /**
     * 
     * @type {Date}
     * @memberof InsightsDashboardResponse
     */
    timestamp: Date;
    /**
     * 
     * @type {Array<NoteInsightData>}
     * @memberof InsightsDashboardResponse
     */
    notes: Array<NoteInsightData>;
    /**
     * 
     * @type {Array<ScheduledEventInsightData>}
     * @memberof InsightsDashboardResponse
     */
    scheduledEvents: Array<ScheduledEventInsightData>;
    /**
     * 
     * @type {Array<TaskInsightData>}
     * @memberof InsightsDashboardResponse
     */
    tasks: Array<TaskInsightData>;
    /**
     * 
     * @type {Array<StructuredMeetingDataInsightData>}
     * @memberof InsightsDashboardResponse
     */
    structuredMeetingData: Array<StructuredMeetingDataInsightData>;
    /**
     * 
     * @type {Array<User>}
     * @memberof InsightsDashboardResponse
     */
    users: Array<User>;
    /**
     * 
     * @type {Array<DeepinsightsCoreMetricsCustomerInsightsClient>}
     * @memberof InsightsDashboardResponse
     */
    clients: Array<DeepinsightsCoreMetricsCustomerInsightsClient>;
    /**
     * 
     * @type {Status}
     * @memberof InsightsDashboardResponse
     */
    status: Status;
}



/**
 * Check if a given object implements the InsightsDashboardResponse interface.
 */
export function instanceOfInsightsDashboardResponse(value: object): value is InsightsDashboardResponse {
    if (!('timestamp' in value) || value['timestamp'] === undefined) return false;
    if (!('notes' in value) || value['notes'] === undefined) return false;
    if (!('scheduledEvents' in value) || value['scheduledEvents'] === undefined) return false;
    if (!('tasks' in value) || value['tasks'] === undefined) return false;
    if (!('structuredMeetingData' in value) || value['structuredMeetingData'] === undefined) return false;
    if (!('users' in value) || value['users'] === undefined) return false;
    if (!('clients' in value) || value['clients'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function InsightsDashboardResponseFromJSON(json: any): InsightsDashboardResponse {
    return InsightsDashboardResponseFromJSONTyped(json, false);
}

export function InsightsDashboardResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): InsightsDashboardResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'timestamp': (new Date(json['timestamp'])),
        'notes': ((json['notes'] as Array<any>).map(NoteInsightDataFromJSON)),
        'scheduledEvents': ((json['scheduled_events'] as Array<any>).map(ScheduledEventInsightDataFromJSON)),
        'tasks': ((json['tasks'] as Array<any>).map(TaskInsightDataFromJSON)),
        'structuredMeetingData': ((json['structured_meeting_data'] as Array<any>).map(StructuredMeetingDataInsightDataFromJSON)),
        'users': ((json['users'] as Array<any>).map(UserFromJSON)),
        'clients': ((json['clients'] as Array<any>).map(DeepinsightsCoreMetricsCustomerInsightsClientFromJSON)),
        'status': StatusFromJSON(json['status']),
    };
}

export function InsightsDashboardResponseToJSON(value?: InsightsDashboardResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'timestamp': ((value['timestamp']).toISOString()),
        'notes': ((value['notes'] as Array<any>).map(NoteInsightDataToJSON)),
        'scheduled_events': ((value['scheduledEvents'] as Array<any>).map(ScheduledEventInsightDataToJSON)),
        'tasks': ((value['tasks'] as Array<any>).map(TaskInsightDataToJSON)),
        'structured_meeting_data': ((value['structuredMeetingData'] as Array<any>).map(StructuredMeetingDataInsightDataToJSON)),
        'users': ((value['users'] as Array<any>).map(UserToJSON)),
        'clients': ((value['clients'] as Array<any>).map(DeepinsightsCoreMetricsCustomerInsightsClientToJSON)),
        'status': StatusToJSON(value['status']),
    };
}

