/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface TaskInsightData
 */
export interface TaskInsightData {
    /**
     * 
     * @type {string}
     * @memberof TaskInsightData
     */
    taskUuid: string;
    /**
     * 
     * @type {string}
     * @memberof TaskInsightData
     */
    assigneeUuid: string | null;
    /**
     * 
     * @type {Date}
     * @memberof TaskInsightData
     */
    creationDate: Date;
    /**
     * 
     * @type {boolean}
     * @memberof TaskInsightData
     */
    completed: boolean;
}

/**
 * Check if a given object implements the TaskInsightData interface.
 */
export function instanceOfTaskInsightData(value: object): value is TaskInsightData {
    if (!('taskUuid' in value) || value['taskUuid'] === undefined) return false;
    if (!('assigneeUuid' in value) || value['assigneeUuid'] === undefined) return false;
    if (!('creationDate' in value) || value['creationDate'] === undefined) return false;
    if (!('completed' in value) || value['completed'] === undefined) return false;
    return true;
}

export function TaskInsightDataFromJSON(json: any): TaskInsightData {
    return TaskInsightDataFromJSONTyped(json, false);
}

export function TaskInsightDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): TaskInsightData {
    if (json == null) {
        return json;
    }
    return {
        
        'taskUuid': json['task_uuid'],
        'assigneeUuid': json['assignee_uuid'],
        'creationDate': (new Date(json['creation_date'])),
        'completed': json['completed'],
    };
}

export function TaskInsightDataToJSON(value?: TaskInsightData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'task_uuid': value['taskUuid'],
        'assignee_uuid': value['assigneeUuid'],
        'creation_date': ((value['creationDate']).toISOString()),
        'completed': value['completed'],
    };
}

