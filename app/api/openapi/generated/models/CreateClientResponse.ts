/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CreateClientResponse
 */
export interface CreateClientResponse {
    /**
     * 
     * @type {string}
     * @memberof CreateClientResponse
     */
    clientUuid: string;
}

/**
 * Check if a given object implements the CreateClientResponse interface.
 */
export function instanceOfCreateClientResponse(value: object): value is CreateClientResponse {
    if (!('clientUuid' in value) || value['clientUuid'] === undefined) return false;
    return true;
}

export function CreateClientResponseFromJSON(json: any): CreateClientResponse {
    return CreateClientResponseFromJSONTyped(json, false);
}

export function CreateClientResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateClientResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'clientUuid': json['client_uuid'],
    };
}

export function CreateClientResponseToJSON(value?: CreateClientResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'client_uuid': value['clientUuid'],
    };
}

