/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GenerateMeetingPrepRequest
 */
export interface GenerateMeetingPrepRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof GenerateMeetingPrepRequest
     */
    clientIds?: Array<string> | null;
    /**
     * 
     * @type {string}
     * @memberof GenerateMeetingPrepRequest
     */
    meetingTypeUuid?: string | null;
}

/**
 * Check if a given object implements the GenerateMeetingPrepRequest interface.
 */
export function instanceOfGenerateMeetingPrepRequest(value: object): value is GenerateMeetingPrepRequest {
    return true;
}

export function GenerateMeetingPrepRequestFromJSON(json: any): GenerateMeetingPrepRequest {
    return GenerateMeetingPrepRequestFromJSONTyped(json, false);
}

export function GenerateMeetingPrepRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GenerateMeetingPrepRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'clientIds': json['client_ids'] == null ? undefined : json['client_ids'],
        'meetingTypeUuid': json['meeting_type_uuid'] == null ? undefined : json['meeting_type_uuid'],
    };
}

export function GenerateMeetingPrepRequestToJSON(value?: GenerateMeetingPrepRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'client_ids': value['clientIds'],
        'meeting_type_uuid': value['meetingTypeUuid'],
    };
}

