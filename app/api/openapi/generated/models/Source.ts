/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const Source = {
    Client: 'client',
    Advisor: 'advisor',
    Unknown: 'unknown'
} as const;
export type Source = typeof Source[keyof typeof Source];


export function instanceOfSource(value: any): boolean {
    for (const key in Source) {
        if (Object.prototype.hasOwnProperty.call(Source, key)) {
            if (Source[key as keyof typeof Source] === value) {
                return true;
            }
        }
    }
    return false;
}

export function SourceFromJSON(json: any): Source {
    return SourceFromJSONTyped(json, false);
}

export function SourceFromJSONTyped(json: any, ignoreDiscriminator: boolean): Source {
    return json as Source;
}

export function SourceToJSON(value?: Source | null): any {
    return value as any;
}

