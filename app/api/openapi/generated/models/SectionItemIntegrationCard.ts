/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';

/**
 * 
 * @export
 * @interface SectionItemIntegrationCard
 */
export interface SectionItemIntegrationCard {
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionItemIntegrationCard
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    tag: string;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemIntegrationCard
     */
    isActive: boolean;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    redirectPath?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    value?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemIntegrationCard
     */
    isInteractible?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof SectionItemIntegrationCard
     */
    isNew?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    errorMsg?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SectionItemIntegrationCard
     */
    ctaId?: string | null;
}



/**
 * Check if a given object implements the SectionItemIntegrationCard interface.
 */
export function instanceOfSectionItemIntegrationCard(value: object): value is SectionItemIntegrationCard {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    if (!('tag' in value) || value['tag'] === undefined) return false;
    if (!('isActive' in value) || value['isActive'] === undefined) return false;
    return true;
}

export function SectionItemIntegrationCardFromJSON(json: any): SectionItemIntegrationCard {
    return SectionItemIntegrationCardFromJSONTyped(json, false);
}

export function SectionItemIntegrationCardFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionItemIntegrationCard {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'tag': json['tag'],
        'isActive': json['isActive'],
        'redirectPath': json['redirectPath'] == null ? undefined : json['redirectPath'],
        'value': json['value'] == null ? undefined : json['value'],
        'isInteractible': json['isInteractible'] == null ? undefined : json['isInteractible'],
        'isNew': json['isNew'] == null ? undefined : json['isNew'],
        'errorMsg': json['errorMsg'] == null ? undefined : json['errorMsg'],
        'ctaId': json['ctaId'] == null ? undefined : json['ctaId'],
    };
}

export function SectionItemIntegrationCardToJSON(value?: SectionItemIntegrationCard | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'tag': value['tag'],
        'isActive': value['isActive'],
        'redirectPath': value['redirectPath'],
        'value': value['value'],
        'isInteractible': value['isInteractible'],
        'isNew': value['isNew'],
        'errorMsg': value['errorMsg'],
        'ctaId': value['ctaId'],
    };
}

