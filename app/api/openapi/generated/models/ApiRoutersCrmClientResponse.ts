/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientType } from './ClientType';
import {
    ClientTypeFromJSON,
    ClientTypeFromJSONTyped,
    ClientTypeToJSON,
} from './ClientType';

/**
 * 
 * @export
 * @interface ApiRoutersCrmClientResponse
 */
export interface ApiRoutersCrmClientResponse {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersCrmClientResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersCrmClientResponse
     */
    name: string;
    /**
     * 
     * @type {ClientType}
     * @memberof ApiRoutersCrmClientResponse
     */
    type: ClientType;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersCrmClientResponse
     */
    crmId?: string | null;
}



/**
 * Check if a given object implements the ApiRoutersCrmClientResponse interface.
 */
export function instanceOfApiRoutersCrmClientResponse(value: object): value is ApiRoutersCrmClientResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('type' in value) || value['type'] === undefined) return false;
    return true;
}

export function ApiRoutersCrmClientResponseFromJSON(json: any): ApiRoutersCrmClientResponse {
    return ApiRoutersCrmClientResponseFromJSONTyped(json, false);
}

export function ApiRoutersCrmClientResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersCrmClientResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'type': ClientTypeFromJSON(json['type']),
        'crmId': json['crm_id'] == null ? undefined : json['crm_id'],
    };
}

export function ApiRoutersCrmClientResponseToJSON(value?: ApiRoutersCrmClientResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'type': ClientTypeToJSON(value['type']),
        'crm_id': value['crmId'],
    };
}

