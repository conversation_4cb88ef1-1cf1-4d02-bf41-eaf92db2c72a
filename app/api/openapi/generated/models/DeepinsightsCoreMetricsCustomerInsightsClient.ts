/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { LifePhase } from './LifePhase';
import {
    LifePhaseFromJSON,
    LifePhaseFromJSONTyped,
    LifePhaseToJSON,
} from './LifePhase';
import type { Segment } from './Segment';
import {
    SegmentFromJSON,
    SegmentFromJSONTyped,
    SegmentToJSON,
} from './Segment';

/**
 * 
 * @export
 * @interface DeepinsightsCoreMetricsCustomerInsightsClient
 */
export interface DeepinsightsCoreMetricsCustomerInsightsClient {
    /**
     * 
     * @type {string}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    name: string;
    /**
     * 
     * @type {LifePhase}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    lifePhase?: LifePhase | null;
    /**
     * 
     * @type {string}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    assetsUnderManagement?: string | null;
    /**
     * 
     * @type {Segment}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    segment?: Segment | null;
    /**
     * 
     * @type {Date}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    onboardingDate?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    isPriority?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof DeepinsightsCoreMetricsCustomerInsightsClient
     */
    hasClientRecap: boolean;
}



/**
 * Check if a given object implements the DeepinsightsCoreMetricsCustomerInsightsClient interface.
 */
export function instanceOfDeepinsightsCoreMetricsCustomerInsightsClient(value: object): value is DeepinsightsCoreMetricsCustomerInsightsClient {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('hasClientRecap' in value) || value['hasClientRecap'] === undefined) return false;
    return true;
}

export function DeepinsightsCoreMetricsCustomerInsightsClientFromJSON(json: any): DeepinsightsCoreMetricsCustomerInsightsClient {
    return DeepinsightsCoreMetricsCustomerInsightsClientFromJSONTyped(json, false);
}

export function DeepinsightsCoreMetricsCustomerInsightsClientFromJSONTyped(json: any, ignoreDiscriminator: boolean): DeepinsightsCoreMetricsCustomerInsightsClient {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'lifePhase': json['life_phase'] == null ? undefined : LifePhaseFromJSON(json['life_phase']),
        'assetsUnderManagement': json['assets_under_management'] == null ? undefined : json['assets_under_management'],
        'segment': json['segment'] == null ? undefined : SegmentFromJSON(json['segment']),
        'onboardingDate': json['onboarding_date'] == null ? undefined : (new Date(json['onboarding_date'])),
        'isPriority': json['is_priority'] == null ? undefined : json['is_priority'],
        'hasClientRecap': json['has_client_recap'],
    };
}

export function DeepinsightsCoreMetricsCustomerInsightsClientToJSON(value?: DeepinsightsCoreMetricsCustomerInsightsClient | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'life_phase': LifePhaseToJSON(value['lifePhase']),
        'assets_under_management': value['assetsUnderManagement'],
        'segment': SegmentToJSON(value['segment']),
        'onboarding_date': value['onboardingDate'] == null ? undefined : ((value['onboardingDate'] as any).toISOString().substring(0,10)),
        'is_priority': value['isPriority'],
        'has_client_recap': value['hasClientRecap'],
    };
}

