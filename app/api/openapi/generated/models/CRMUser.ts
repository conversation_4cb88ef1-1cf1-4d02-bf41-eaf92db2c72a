/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ZeplynUser } from './ZeplynUser';
import {
    ZeplynUserFromJSON,
    ZeplynUserFromJSONTyped,
    ZeplynUserToJSON,
} from './ZeplynUser';
import type { ZeplynOrganization } from './ZeplynOrganization';
import {
    ZeplynOrganizationFromJSON,
    ZeplynOrganizationFromJSONTyped,
    ZeplynOrganizationToJSON,
} from './ZeplynOrganization';

/**
 * 
 * @export
 * @interface CRMUser
 */
export interface CRMUser {
    /**
     * 
     * @type {string}
     * @memberof CRMUser
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof CRMUser
     */
    crmId: string;
    /**
     * 
     * @type {ZeplynOrganization}
     * @memberof CRMUser
     */
    organization: ZeplynOrganization;
    /**
     * 
     * @type {ZeplynUser}
     * @memberof CRMUser
     */
    zeplynUser?: ZeplynUser | null;
}

/**
 * Check if a given object implements the CRMUser interface.
 */
export function instanceOfCRMUser(value: object): value is CRMUser {
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('crmId' in value) || value['crmId'] === undefined) return false;
    if (!('organization' in value) || value['organization'] === undefined) return false;
    return true;
}

export function CRMUserFromJSON(json: any): CRMUser {
    return CRMUserFromJSONTyped(json, false);
}

export function CRMUserFromJSONTyped(json: any, ignoreDiscriminator: boolean): CRMUser {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'crmId': json['crm_id'],
        'organization': ZeplynOrganizationFromJSON(json['organization']),
        'zeplynUser': json['zeplyn_user'] == null ? undefined : ZeplynUserFromJSON(json['zeplyn_user']),
    };
}

export function CRMUserToJSON(value?: CRMUser | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'crm_id': value['crmId'],
        'organization': ZeplynOrganizationToJSON(value['organization']),
        'zeplyn_user': ZeplynUserToJSON(value['zeplynUser']),
    };
}

