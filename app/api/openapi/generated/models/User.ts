/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface User
 */
export interface User {
    /**
     * 
     * @type {string}
     * @memberof User
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    email: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    name: string | null;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    managerUuid?: string | null;
}

/**
 * Check if a given object implements the User interface.
 */
export function instanceOfUser(value: object): value is User {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function UserFromJSON(json: any): User {
    return UserFromJSONTyped(json, false);
}

export function UserFromJSONTyped(json: any, ignoreDiscriminator: boolean): User {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'email': json['email'],
        'name': json['name'],
        'managerUuid': json['manager_uuid'] == null ? undefined : json['manager_uuid'],
    };
}

export function UserToJSON(value?: User | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'email': value['email'],
        'name': value['name'],
        'manager_uuid': value['managerUuid'],
    };
}

