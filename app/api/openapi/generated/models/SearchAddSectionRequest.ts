/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * The request model for the add section to summary endpoint
 * @export
 * @interface SearchAddSectionRequest
 */
export interface SearchAddSectionRequest {
    /**
     * 
     * @type {string}
     * @memberof SearchAddSectionRequest
     */
    searchQueryId: string;
    /**
     * 
     * @type {number}
     * @memberof SearchAddSectionRequest
     */
    sectionToReplace?: number | null;
}

/**
 * Check if a given object implements the SearchAddSectionRequest interface.
 */
export function instanceOfSearchAddSectionRequest(value: object): value is SearchAddSectionRequest {
    if (!('searchQueryId' in value) || value['searchQueryId'] === undefined) return false;
    return true;
}

export function SearchAddSectionRequestFromJSON(json: any): SearchAddSectionRequest {
    return SearchAddSectionRequestFromJSONTyped(json, false);
}

export function SearchAddSectionRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): SearchAddSectionRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'searchQueryId': json['search_query_id'],
        'sectionToReplace': json['section_to_replace'] == null ? undefined : json['section_to_replace'],
    };
}

export function SearchAddSectionRequestToJSON(value?: SearchAddSectionRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'search_query_id': value['searchQueryId'],
        'section_to_replace': value['sectionToReplace'],
    };
}

