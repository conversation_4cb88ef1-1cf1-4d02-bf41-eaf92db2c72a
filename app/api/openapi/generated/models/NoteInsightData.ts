/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Tag } from './Tag';
import {
    TagFromJSON,
    TagFromJSONTyped,
    TagToJSON,
} from './Tag';
import type { ProcessingStatus } from './ProcessingStatus';
import {
    ProcessingStatusFromJSON,
    ProcessingStatusFromJSONTyped,
    ProcessingStatusToJSON,
} from './ProcessingStatus';

/**
 * 
 * @export
 * @interface NoteInsightData
 */
export interface NoteInsightData {
    /**
     * 
     * @type {string}
     * @memberof NoteInsightData
     */
    meetingUuid: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteInsightData
     */
    authorizedUserUuids: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof NoteInsightData
     */
    source: NoteInsightDataSourceEnum;
    /**
     * 
     * @type {number}
     * @memberof NoteInsightData
     */
    durationSeconds: number | null;
    /**
     * 
     * @type {ProcessingStatus}
     * @memberof NoteInsightData
     */
    status: ProcessingStatus;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteInsightData
     */
    clientUuids: Array<string>;
    /**
     * 
     * @type {boolean}
     * @memberof NoteInsightData
     */
    meetingPrepGenerated: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof NoteInsightData
     */
    followUpGenerated: boolean;
    /**
     * 
     * @type {string}
     * @memberof NoteInsightData
     */
    scheduledEventUuid: string | null;
    /**
     * 
     * @type {Date}
     * @memberof NoteInsightData
     */
    startTime: Date;
    /**
     * 
     * @type {Array<Tag>}
     * @memberof NoteInsightData
     */
    tags: Array<Tag>;
}


/**
 * @export
 */
export const NoteInsightDataSourceEnum = {
    Unknown: 'Unknown',
    Mic: 'Mic',
    Notetaker: 'Notetaker',
    PhoneCall: 'Phone call'
} as const;
export type NoteInsightDataSourceEnum = typeof NoteInsightDataSourceEnum[keyof typeof NoteInsightDataSourceEnum];


/**
 * Check if a given object implements the NoteInsightData interface.
 */
export function instanceOfNoteInsightData(value: object): value is NoteInsightData {
    if (!('meetingUuid' in value) || value['meetingUuid'] === undefined) return false;
    if (!('authorizedUserUuids' in value) || value['authorizedUserUuids'] === undefined) return false;
    if (!('source' in value) || value['source'] === undefined) return false;
    if (!('durationSeconds' in value) || value['durationSeconds'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('clientUuids' in value) || value['clientUuids'] === undefined) return false;
    if (!('meetingPrepGenerated' in value) || value['meetingPrepGenerated'] === undefined) return false;
    if (!('followUpGenerated' in value) || value['followUpGenerated'] === undefined) return false;
    if (!('scheduledEventUuid' in value) || value['scheduledEventUuid'] === undefined) return false;
    if (!('startTime' in value) || value['startTime'] === undefined) return false;
    if (!('tags' in value) || value['tags'] === undefined) return false;
    return true;
}

export function NoteInsightDataFromJSON(json: any): NoteInsightData {
    return NoteInsightDataFromJSONTyped(json, false);
}

export function NoteInsightDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): NoteInsightData {
    if (json == null) {
        return json;
    }
    return {
        
        'meetingUuid': json['meeting_uuid'],
        'authorizedUserUuids': json['authorized_user_uuids'],
        'source': json['source'],
        'durationSeconds': json['duration_seconds'],
        'status': ProcessingStatusFromJSON(json['status']),
        'clientUuids': json['client_uuids'],
        'meetingPrepGenerated': json['meeting_prep_generated'],
        'followUpGenerated': json['follow_up_generated'],
        'scheduledEventUuid': json['scheduled_event_uuid'],
        'startTime': (new Date(json['start_time'])),
        'tags': ((json['tags'] as Array<any>).map(TagFromJSON)),
    };
}

export function NoteInsightDataToJSON(value?: NoteInsightData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'meeting_uuid': value['meetingUuid'],
        'authorized_user_uuids': value['authorizedUserUuids'],
        'source': value['source'],
        'duration_seconds': value['durationSeconds'],
        'status': ProcessingStatusToJSON(value['status']),
        'client_uuids': value['clientUuids'],
        'meeting_prep_generated': value['meetingPrepGenerated'],
        'follow_up_generated': value['followUpGenerated'],
        'scheduled_event_uuid': value['scheduledEventUuid'],
        'start_time': ((value['startTime']).toISOString()),
        'tags': ((value['tags'] as Array<any>).map(TagToJSON)),
    };
}

