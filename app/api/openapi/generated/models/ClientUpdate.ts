/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientType } from './ClientType';
import {
    ClientTypeFromJSON,
    ClientTypeFromJSONTyped,
    ClientTypeToJSON,
} from './ClientType';

/**
 * 
 * @export
 * @interface ClientUpdate
 */
export interface ClientUpdate {
    /**
     * 
     * @type {string}
     * @memberof ClientUpdate
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientUpdate
     */
    firstName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientUpdate
     */
    lastName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientUpdate
     */
    jobTitle?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientUpdate
     */
    email?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ClientUpdate
     */
    phoneNumber?: string;
    /**
     * 
     * @type {Date}
     * @memberof ClientUpdate
     */
    onboardingDate?: Date | null;
    /**
     * 
     * @type {ClientType}
     * @memberof ClientUpdate
     */
    type?: ClientType;
    /**
     * 
     * @type {Date}
     * @memberof ClientUpdate
     */
    dateOfBirth?: Date | null;
}



/**
 * Check if a given object implements the ClientUpdate interface.
 */
export function instanceOfClientUpdate(value: object): value is ClientUpdate {
    return true;
}

export function ClientUpdateFromJSON(json: any): ClientUpdate {
    return ClientUpdateFromJSONTyped(json, false);
}

export function ClientUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'] == null ? undefined : json['name'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'jobTitle': json['job_title'] == null ? undefined : json['job_title'],
        'email': json['email'] == null ? undefined : json['email'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
        'onboardingDate': json['onboarding_date'] == null ? undefined : (new Date(json['onboarding_date'])),
        'type': json['type'] == null ? undefined : ClientTypeFromJSON(json['type']),
        'dateOfBirth': json['date_of_birth'] == null ? undefined : (new Date(json['date_of_birth'])),
    };
}

export function ClientUpdateToJSON(value?: ClientUpdate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'job_title': value['jobTitle'],
        'email': value['email'],
        'phone_number': value['phoneNumber'],
        'onboarding_date': value['onboardingDate'] == null ? undefined : ((value['onboardingDate'] as any).toISOString().substring(0,10)),
        'type': ClientTypeToJSON(value['type']),
        'date_of_birth': value['dateOfBirth'] == null ? undefined : ((value['dateOfBirth'] as any).toISOString().substring(0,10)),
    };
}

