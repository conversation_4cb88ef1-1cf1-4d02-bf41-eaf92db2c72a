/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const LifePhase = {
    Accumulation: 'accumulation',
    Consolidation: 'consolidation',
    Distribution: 'distribution',
    Retirement: 'retirement'
} as const;
export type LifePhase = typeof LifePhase[keyof typeof LifePhase];


export function instanceOfLifePhase(value: any): boolean {
    for (const key in LifePhase) {
        if (Object.prototype.hasOwnProperty.call(LifePhase, key)) {
            if (LifePhase[key as keyof typeof LifePhase] === value) {
                return true;
            }
        }
    }
    return false;
}

export function LifePhaseFromJSON(json: any): LifePhase {
    return LifePhaseFromJSONTyped(json, false);
}

export function LifePhaseFromJSONTyped(json: any, ignoreDiscriminator: boolean): LifePhase {
    return json as LifePhase;
}

export function LifePhaseToJSON(value?: LifePhase | null): any {
    return value as any;
}

