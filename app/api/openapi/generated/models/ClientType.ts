/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const ClientType = {
    Individual: 'individual',
    Household: 'household',
    Unknown: 'unknown'
} as const;
export type ClientType = typeof ClientType[keyof typeof ClientType];


export function instanceOfClientType(value: any): boolean {
    for (const key in ClientType) {
        if (Object.prototype.hasOwnProperty.call(ClientType, key)) {
            if (ClientType[key as keyof typeof ClientType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ClientTypeFromJSON(json: any): ClientType {
    return ClientTypeFromJSONTyped(json, false);
}

export function ClientTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientType {
    return json as ClientType;
}

export function ClientTypeToJSON(value?: ClientType | null): any {
    return value as any;
}

