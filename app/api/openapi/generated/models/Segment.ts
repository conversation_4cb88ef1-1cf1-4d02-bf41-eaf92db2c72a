/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const Segment = {
    Bronze: 'bronze',
    Silver: 'silver',
    Gold: 'gold',
    Platinum: 'platinum'
} as const;
export type Segment = typeof Segment[keyof typeof Segment];


export function instanceOfSegment(value: any): boolean {
    for (const key in Segment) {
        if (Object.prototype.hasOwnProperty.call(Segment, key)) {
            if (Segment[key as keyof typeof Segment] === value) {
                return true;
            }
        }
    }
    return false;
}

export function SegmentFromJSON(json: any): Segment {
    return SegmentFromJSONTyped(json, false);
}

export function SegmentFromJSONTyped(json: any, ignoreDiscriminator: boolean): Segment {
    return json as Segment;
}

export function SegmentToJSON(value?: Segment | null): any {
    return value as any;
}

