/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CRMUser } from './CRMUser';
import {
    CRMUserFromJSON,
    CRMUserFromJSONTyped,
    CRMUserToJSON,
} from './CRMUser';

/**
 * 
 * @export
 * @interface TaskUpdate
 */
export interface TaskUpdate {
    /**
     * 
     * @type {string}
     * @memberof TaskUpdate
     */
    title?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TaskUpdate
     */
    description?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof TaskUpdate
     */
    dueDate?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof TaskUpdate
     */
    completed?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof TaskUpdate
     */
    assignee?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TaskUpdate
     */
    parentNoteUuid?: string | null;
    /**
     * 
     * @type {CRMUser}
     * @memberof TaskUpdate
     */
    crmAssignee?: CRMUser | null;
}

/**
 * Check if a given object implements the TaskUpdate interface.
 */
export function instanceOfTaskUpdate(value: object): value is TaskUpdate {
    return true;
}

export function TaskUpdateFromJSON(json: any): TaskUpdate {
    return TaskUpdateFromJSONTyped(json, false);
}

export function TaskUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): TaskUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'] == null ? undefined : json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'dueDate': json['due_date'] == null ? undefined : (new Date(json['due_date'])),
        'completed': json['completed'] == null ? undefined : json['completed'],
        'assignee': json['assignee'] == null ? undefined : json['assignee'],
        'parentNoteUuid': json['parent_note_uuid'] == null ? undefined : json['parent_note_uuid'],
        'crmAssignee': json['crm_assignee'] == null ? undefined : CRMUserFromJSON(json['crm_assignee']),
    };
}

export function TaskUpdateToJSON(value?: TaskUpdate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'title': value['title'],
        'description': value['description'],
        'due_date': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString()),
        'completed': value['completed'],
        'assignee': value['assignee'],
        'parent_note_uuid': value['parentNoteUuid'],
        'crm_assignee': CRMUserToJSON(value['crmAssignee']),
    };
}

