/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ScheduledEventInsightData
 */
export interface ScheduledEventInsightData {
    /**
     * 
     * @type {string}
     * @memberof ScheduledEventInsightData
     */
    scheduledEventUuid: string;
    /**
     * 
     * @type {string}
     * @memberof ScheduledEventInsightData
     */
    userUuid: string;
    /**
     * 
     * @type {boolean}
     * @memberof ScheduledEventInsightData
     */
    hasClients: boolean;
}

/**
 * Check if a given object implements the ScheduledEventInsightData interface.
 */
export function instanceOfScheduledEventInsightData(value: object): value is ScheduledEventInsightData {
    if (!('scheduledEventUuid' in value) || value['scheduledEventUuid'] === undefined) return false;
    if (!('userUuid' in value) || value['userUuid'] === undefined) return false;
    if (!('hasClients' in value) || value['hasClients'] === undefined) return false;
    return true;
}

export function ScheduledEventInsightDataFromJSON(json: any): ScheduledEventInsightData {
    return ScheduledEventInsightDataFromJSONTyped(json, false);
}

export function ScheduledEventInsightDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): ScheduledEventInsightData {
    if (json == null) {
        return json;
    }
    return {
        
        'scheduledEventUuid': json['scheduled_event_uuid'],
        'userUuid': json['user_uuid'],
        'hasClients': json['has_clients'],
    };
}

export function ScheduledEventInsightDataToJSON(value?: ScheduledEventInsightData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'scheduled_event_uuid': value['scheduledEventUuid'],
        'user_uuid': value['userUuid'],
        'has_clients': value['hasClients'],
    };
}

