/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ZeplynOrganization
 */
export interface ZeplynOrganization {
    /**
     * 
     * @type {string}
     * @memberof ZeplynOrganization
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ZeplynOrganization
     */
    name: string;
}

/**
 * Check if a given object implements the ZeplynOrganization interface.
 */
export function instanceOfZeplynOrganization(value: object): value is ZeplynOrganization {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function ZeplynOrganizationFromJSON(json: any): ZeplynOrganization {
    return ZeplynOrganizationFromJSONTyped(json, false);
}

export function ZeplynOrganizationFromJSONTyped(json: any, ignoreDiscriminator: boolean): ZeplynOrganization {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
    };
}

export function ZeplynOrganizationToJSON(value?: ZeplynOrganization | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
    };
}

