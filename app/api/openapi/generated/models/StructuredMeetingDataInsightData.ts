/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface StructuredMeetingDataInsightData
 */
export interface StructuredMeetingDataInsightData {
    /**
     * 
     * @type {string}
     * @memberof StructuredMeetingDataInsightData
     */
    meetingDataUuid: string;
    /**
     * 
     * @type {string}
     * @memberof StructuredMeetingDataInsightData
     */
    noteUuid: string | null;
    /**
     * 
     * @type {string}
     * @memberof StructuredMeetingDataInsightData
     */
    title: string | null;
    /**
     * 
     * @type {string}
     * @memberof StructuredMeetingDataInsightData
     */
    kind: string;
    /**
     * 
     * @type {number}
     * @memberof StructuredMeetingDataInsightData
     */
    itemsTotal: number;
    /**
     * 
     * @type {number}
     * @memberof StructuredMeetingDataInsightData
     */
    itemsCompleted: number;
}

/**
 * Check if a given object implements the StructuredMeetingDataInsightData interface.
 */
export function instanceOfStructuredMeetingDataInsightData(value: object): value is StructuredMeetingDataInsightData {
    if (!('meetingDataUuid' in value) || value['meetingDataUuid'] === undefined) return false;
    if (!('noteUuid' in value) || value['noteUuid'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('kind' in value) || value['kind'] === undefined) return false;
    if (!('itemsTotal' in value) || value['itemsTotal'] === undefined) return false;
    if (!('itemsCompleted' in value) || value['itemsCompleted'] === undefined) return false;
    return true;
}

export function StructuredMeetingDataInsightDataFromJSON(json: any): StructuredMeetingDataInsightData {
    return StructuredMeetingDataInsightDataFromJSONTyped(json, false);
}

export function StructuredMeetingDataInsightDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): StructuredMeetingDataInsightData {
    if (json == null) {
        return json;
    }
    return {
        
        'meetingDataUuid': json['meeting_data_uuid'],
        'noteUuid': json['note_uuid'],
        'title': json['title'],
        'kind': json['kind'],
        'itemsTotal': json['items_total'],
        'itemsCompleted': json['items_completed'],
    };
}

export function StructuredMeetingDataInsightDataToJSON(value?: StructuredMeetingDataInsightData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'meeting_data_uuid': value['meetingDataUuid'],
        'note_uuid': value['noteUuid'],
        'title': value['title'],
        'kind': value['kind'],
        'items_total': value['itemsTotal'],
        'items_completed': value['itemsCompleted'],
    };
}

