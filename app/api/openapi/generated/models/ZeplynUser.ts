/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ZeplynUser
 */
export interface ZeplynUser {
    /**
     * 
     * @type {string}
     * @memberof ZeplynUser
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ZeplynUser
     */
    email: string;
    /**
     * 
     * @type {string}
     * @memberof ZeplynUser
     */
    name?: string | null;
}

/**
 * Check if a given object implements the ZeplynUser interface.
 */
export function instanceOfZeplynUser(value: object): value is ZeplynUser {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    return true;
}

export function ZeplynUserFromJSON(json: any): ZeplynUser {
    return ZeplynUserFromJSONTyped(json, false);
}

export function ZeplynUserFromJSONTyped(json: any, ignoreDiscriminator: boolean): ZeplynUser {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'email': json['email'],
        'name': json['name'] == null ? undefined : json['name'],
    };
}

export function ZeplynUserToJSON(value?: ZeplynUser | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'email': value['email'],
        'name': value['name'],
    };
}

