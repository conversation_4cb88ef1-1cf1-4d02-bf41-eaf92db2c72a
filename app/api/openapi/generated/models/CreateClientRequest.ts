/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientType } from './ClientType';
import {
    ClientTypeFromJSON,
    ClientTypeFromJSONTyped,
    ClientTypeToJSON,
} from './ClientType';

/**
 * 
 * @export
 * @interface CreateClientRequest
 */
export interface CreateClientRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateClientRequest
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof CreateClientRequest
     */
    firstName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateClientRequest
     */
    lastName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateClientRequest
     */
    jobTitle?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateClientRequest
     */
    email?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateClientRequest
     */
    phoneNumber?: string;
    /**
     * 
     * @type {Date}
     * @memberof CreateClientRequest
     */
    onboardingDate?: Date | null;
    /**
     * 
     * @type {ClientType}
     * @memberof CreateClientRequest
     */
    type?: ClientType;
    /**
     * 
     * @type {Date}
     * @memberof CreateClientRequest
     */
    dateOfBirth?: Date | null;
}



/**
 * Check if a given object implements the CreateClientRequest interface.
 */
export function instanceOfCreateClientRequest(value: object): value is CreateClientRequest {
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function CreateClientRequestFromJSON(json: any): CreateClientRequest {
    return CreateClientRequestFromJSONTyped(json, false);
}

export function CreateClientRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateClientRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'jobTitle': json['job_title'] == null ? undefined : json['job_title'],
        'email': json['email'] == null ? undefined : json['email'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
        'onboardingDate': json['onboarding_date'] == null ? undefined : (new Date(json['onboarding_date'])),
        'type': json['type'] == null ? undefined : ClientTypeFromJSON(json['type']),
        'dateOfBirth': json['date_of_birth'] == null ? undefined : (new Date(json['date_of_birth'])),
    };
}

export function CreateClientRequestToJSON(value?: CreateClientRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'job_title': value['jobTitle'],
        'email': value['email'],
        'phone_number': value['phoneNumber'],
        'onboarding_date': value['onboardingDate'] == null ? undefined : ((value['onboardingDate'] as any).toISOString().substring(0,10)),
        'type': ClientTypeToJSON(value['type']),
        'date_of_birth': value['dateOfBirth'] == null ? undefined : ((value['dateOfBirth'] as any).toISOString().substring(0,10)),
    };
}

