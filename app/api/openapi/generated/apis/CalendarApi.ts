/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  BodyCalendarUpdateAutojoin,
  HTTPValidationError,
  ScheduledEvent,
} from '../models/index';
import {
    BodyCalendarUpdateAutojoinFromJSON,
    BodyCalendarUpdateAutojoinToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    ScheduledEventFromJSON,
    ScheduledEventToJSON,
} from '../models/index';

export interface CalendarScheduledCalendarEventsRequest {
    timeZone: string;
}

export interface CalendarUpdateAutojoinRequest {
    bodyCalendarUpdateAutojoin: BodyCalendarUpdateAutojoin;
}

/**
 * 
 */
export class Calendar<PERSON>pi extends runtime.BaseAPI {

    /**
     * Scheduled Calendar Events
     */
    async calendarScheduledCalendarEventsRaw(requestParameters: CalendarScheduledCalendarEventsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<ScheduledEvent>>> {
        if (requestParameters['timeZone'] == null) {
            throw new runtime.RequiredError(
                'timeZone',
                'Required parameter "timeZone" was null or undefined when calling calendarScheduledCalendarEvents().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['timeZone'] != null) {
            queryParameters['time_zone'] = requestParameters['timeZone'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/calendar/events`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(ScheduledEventFromJSON));
    }

    /**
     * Scheduled Calendar Events
     */
    async calendarScheduledCalendarEvents(requestParameters: CalendarScheduledCalendarEventsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<ScheduledEvent>> {
        const response = await this.calendarScheduledCalendarEventsRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update Autojoin
     */
    async calendarUpdateAutojoinRaw(requestParameters: CalendarUpdateAutojoinRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['bodyCalendarUpdateAutojoin'] == null) {
            throw new runtime.RequiredError(
                'bodyCalendarUpdateAutojoin',
                'Required parameter "bodyCalendarUpdateAutojoin" was null or undefined when calling calendarUpdateAutojoin().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/calendar/update_autojoin`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: BodyCalendarUpdateAutojoinToJSON(requestParameters['bodyCalendarUpdateAutojoin']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Update Autojoin
     */
    async calendarUpdateAutojoin(requestParameters: CalendarUpdateAutojoinRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.calendarUpdateAutojoinRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
