/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ClientListResponse,
  HTTPValidationError,
  RedtailCredentials,
  RedtailStatusResponse,
  UploadNoteToCRMRequest,
  UploadNoteToCRMResponse,
} from '../models/index';
import {
    ClientListResponseFromJSON,
    ClientListResponseToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    RedtailCredentialsFromJSON,
    RedtailCredentialsToJSON,
    RedtailStatusResponseFromJSON,
    RedtailStatusResponseToJSON,
    UploadNoteToCRMRequestFromJSON,
    UploadNoteToCRMRequestToJSON,
    UploadNoteToCRMResponseFromJSON,
    UploadNoteToCRMResponseToJSON,
} from '../models/index';

export interface CrmGenerateRedtailUserKeyRequest {
    redtailCredentials: RedtailCredentials;
}

export interface CrmGetClientListRequest {
    q?: string | null;
    cursor?: string | null;
    pageSize?: number | null;
}

export interface CrmUploadNoteToCrmRequest {
    noteId: string;
    syncTargetIds?: Array<string> | null;
    uploadNoteToCRMRequest?: UploadNoteToCRMRequest;
}

/**
 * 
 */
export class CrmApi extends runtime.BaseAPI {

    /**
     * Generate Redtail User Key
     */
    async crmGenerateRedtailUserKeyRaw(requestParameters: CrmGenerateRedtailUserKeyRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['redtailCredentials'] == null) {
            throw new runtime.RequiredError(
                'redtailCredentials',
                'Required parameter "redtailCredentials" was null or undefined when calling crmGenerateRedtailUserKey().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/crm/redtail/generate-key`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: RedtailCredentialsToJSON(requestParameters['redtailCredentials']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Generate Redtail User Key
     */
    async crmGenerateRedtailUserKey(requestParameters: CrmGenerateRedtailUserKeyRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.crmGenerateRedtailUserKeyRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Get Client List
     */
    async crmGetClientListRaw(requestParameters: CrmGetClientListRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ClientListResponse>> {
        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['cursor'] != null) {
            queryParameters['cursor'] = requestParameters['cursor'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/crm/clients`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ClientListResponseFromJSON(jsonValue));
    }

    /**
     * Get Client List
     */
    async crmGetClientList(requestParameters: CrmGetClientListRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ClientListResponse> {
        const response = await this.crmGetClientListRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get Redtail Integration Status
     */
    async crmGetRedtailIntegrationStatusRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RedtailStatusResponse>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/crm/redtail/status`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RedtailStatusResponseFromJSON(jsonValue));
    }

    /**
     * Get Redtail Integration Status
     */
    async crmGetRedtailIntegrationStatus(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RedtailStatusResponse> {
        const response = await this.crmGetRedtailIntegrationStatusRaw(initOverrides);
        return await response.value();
    }

    /**
     * Upload Note To Crm
     */
    async crmUploadNoteToCrmRaw(requestParameters: CrmUploadNoteToCrmRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<UploadNoteToCRMResponse>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling crmUploadNoteToCrm().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['syncTargetIds'] != null) {
            queryParameters['sync_target_ids'] = requestParameters['syncTargetIds'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/crm/upload_note/{note_id}`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: UploadNoteToCRMRequestToJSON(requestParameters['uploadNoteToCRMRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => UploadNoteToCRMResponseFromJSON(jsonValue));
    }

    /**
     * Upload Note To Crm
     */
    async crmUploadNoteToCrm(requestParameters: CrmUploadNoteToCrmRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<UploadNoteToCRMResponse | null | undefined > {
        const response = await this.crmUploadNoteToCrmRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

}
