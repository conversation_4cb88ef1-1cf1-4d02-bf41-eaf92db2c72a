/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ApiRoutersClientClientResponse,
  BodyClientGenerateClientRecap,
  ClientRecapResponse,
  ClientUpdate,
  CreateClientRequest,
  CreateClientResponse,
  HTTPValidationError,
} from '../models/index';
import {
    ApiRoutersClientClientResponseFromJSON,
    ApiRoutersClientClientResponseToJSON,
    BodyClientGenerateClientRecapFromJSON,
    BodyClientGenerateClientRecapToJSON,
    ClientRecapResponseFromJSON,
    ClientRecapResponseToJSON,
    ClientUpdateFromJSON,
    ClientUpdateToJSON,
    CreateClientRequestFromJSON,
    CreateClientRequestToJSON,
    CreateClientResponseFromJSON,
    CreateClientResponseToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
} from '../models/index';

export interface ClientCreateClientRequest {
    createClientRequest: CreateClientRequest;
}

export interface ClientEditClientRequest {
    clientUuid: string;
    clientUpdate: ClientUpdate;
}

export interface ClientGenerateClientRecapRequest {
    clientId: string;
    bodyClientGenerateClientRecap?: BodyClientGenerateClientRecap;
}

export interface ClientGetClientRequest {
    clientId: string;
}

/**
 * 
 */
export class ClientApi extends runtime.BaseAPI {

    /**
     * Create a new client owned by the user.
     * Create a new client
     */
    async clientCreateClientRaw(requestParameters: ClientCreateClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CreateClientResponse>> {
        if (requestParameters['createClientRequest'] == null) {
            throw new runtime.RequiredError(
                'createClientRequest',
                'Required parameter "createClientRequest" was null or undefined when calling clientCreateClient().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/client/`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: CreateClientRequestToJSON(requestParameters['createClientRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CreateClientResponseFromJSON(jsonValue));
    }

    /**
     * Create a new client owned by the user.
     * Create a new client
     */
    async clientCreateClient(requestParameters: ClientCreateClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CreateClientResponse> {
        const response = await this.clientCreateClientRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Edit details of an existing client owned by the user.
     * Update an existing client
     */
    async clientEditClientRaw(requestParameters: ClientEditClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['clientUuid'] == null) {
            throw new runtime.RequiredError(
                'clientUuid',
                'Required parameter "clientUuid" was null or undefined when calling clientEditClient().'
            );
        }

        if (requestParameters['clientUpdate'] == null) {
            throw new runtime.RequiredError(
                'clientUpdate',
                'Required parameter "clientUpdate" was null or undefined when calling clientEditClient().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/client/{client_uuid}`.replace(`{${"client_uuid"}}`, encodeURIComponent(String(requestParameters['clientUuid']))),
            method: 'PATCH',
            headers: headerParameters,
            query: queryParameters,
            body: ClientUpdateToJSON(requestParameters['clientUpdate']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Edit details of an existing client owned by the user.
     * Update an existing client
     */
    async clientEditClient(requestParameters: ClientEditClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.clientEditClientRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Initiate the generation of a client recap for a specific client
     * Generate a client recap
     */
    async clientGenerateClientRecapRaw(requestParameters: ClientGenerateClientRecapRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ClientRecapResponse>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling clientGenerateClientRecap().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/client/{client_id}/generate-recap`.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: BodyClientGenerateClientRecapToJSON(requestParameters['bodyClientGenerateClientRecap']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ClientRecapResponseFromJSON(jsonValue));
    }

    /**
     * Initiate the generation of a client recap for a specific client
     * Generate a client recap
     */
    async clientGenerateClientRecap(requestParameters: ClientGenerateClientRecapRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ClientRecapResponse> {
        const response = await this.clientGenerateClientRecapRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get detailed information about a specific client including the client recap, notes, and action items
     * Get client details
     */
    async clientGetClientRaw(requestParameters: ClientGetClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ApiRoutersClientClientResponse>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling clientGetClient().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/client/{client_id}`.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ApiRoutersClientClientResponseFromJSON(jsonValue));
    }

    /**
     * Get detailed information about a specific client including the client recap, notes, and action items
     * Get client details
     */
    async clientGetClient(requestParameters: ClientGetClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ApiRoutersClientClientResponse> {
        const response = await this.clientGetClientRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
