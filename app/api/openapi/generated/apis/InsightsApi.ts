/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  InsightsDashboardResponse,
} from '../models/index';
import {
    InsightsDashboardResponseFromJSON,
    InsightsDashboardResponseToJSON,
} from '../models/index';

/**
 * 
 */
export class InsightsApi extends runtime.BaseAPI {

    /**
     * Returns pre-generated insights data from persistent storage
     * Get insights dashboard data
     */
    async insightsGetInsightsDataRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<InsightsDashboardResponse>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/insights/data`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => InsightsDashboardResponseFromJSON(jsonValue));
    }

    /**
     * Returns pre-generated insights data from persistent storage
     * Get insights dashboard data
     */
    async insightsGetInsightsData(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<InsightsDashboardResponse> {
        const response = await this.insightsGetInsightsDataRaw(initOverrides);
        return await response.value();
    }

    /**
     * Returns raw insights data in a ZIP file containing CSV files for notes, scheduled events, tasks, and structured meeting data
     * Get raw insights data
     */
    async insightsGetRawInsightsDataRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/insights/raw_data`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Returns raw insights data in a ZIP file containing CSV files for notes, scheduled events, tasks, and structured meeting data
     * Get raw insights data
     */
    async insightsGetRawInsightsData(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.insightsGetRawInsightsDataRaw(initOverrides);
        return await response.value();
    }

}
