import { logError } from "~/utils/log.server";
import {
  Configuration,
  OauthApi,
  OAuthRequestCrmTypeEnum,
  OAuthRequestProviderEnum,
} from "../openapi/generated";
import { configurationParameters } from "../openapi/configParams";

export const setUpOAuthProvider = async ({
  authorizationCode,
  provider,
  request,
  codeVerifier,
  crmType,
}: {
  authorizationCode: string;
  provider: OAuthRequestProviderEnum;
  request: Request;
  codeVerifier?: string;
  crmType?: OAuthRequestCrmTypeEnum;
}) => {
  try {
    const requestURLString = request.url.split("?")[0];
    if (!requestURLString) {
      throw Error("Could not parse request URL");
    }
    const requestURL = new URL(requestURLString);
    const originalProto = request.headers.get("X-Forwarded-Proto");
    if (originalProto) {
      requestURL.protocol = `${originalProto}:`;
    }

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    await new OauthApi(configuration).oauthSetUpOauthIntegration({
      oAuthRequest: {
        authorizationCode: authorizationCode,
        requestUrl: requestURL.toString(),
        provider: provider,
        codeVerifier,
        crmType,
      },
    });
  } catch (error) {
    if (error instanceof Response) throw error;
    logError("!!! OAuthApi.oauthSetUpOAuthIntegration", error);
    throw Error("Something went wrong");
  }
};
