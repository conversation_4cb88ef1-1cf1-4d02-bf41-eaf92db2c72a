import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { fetchFlags } from "./flagFetcher.server";
import { logError } from "~/utils/log.server";
import { authenticator } from "~/auth/authenticator.server";
import { FlagsApi } from "../openapi/generated";
import { UserAuthSession } from "~/auth/types";
import {
  configurationParameters,
  nonAuthenticatedConfigurationParameters,
} from "../openapi/configParams";

// Mock dependencies
vi.mock("~/utils/log.server");
vi.mock("~/auth/authenticator.server");
vi.mock("../openapi/generated");
vi.mock("../openapi/configParams");

const mockLogError = vi.mocked(logError);
const mockFlagsFlags = vi.mocked(FlagsApi.prototype.flagsFlags);
const mockNonAuthenticatedConfigurationParameters = vi.mocked(
  nonAuthenticatedConfigurationParameters
);
const mockConfigurationParameters = vi.mocked(configurationParameters);

const mockIsAuthenticated = vi.fn();
vi.mocked(authenticator).isAuthenticated = mockIsAuthenticated;

describe("flagFetcher.server", () => {
  const mockRequest = new Request("http://localhost/test");
  const mockUserSession: UserAuthSession = {
    userId: "123",
    accessToken: "token",
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "User",
  };
  const mockFlags = {
    flags: {
      feature1: { is_active: true },
      feature2: { is_active: false },
    },
    switches: {
      switch1: { is_active: true },
    },
    samples: {
      sample1: { is_active: false },
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should fetch flags for authenticated user with new API", async () => {
    mockFlagsFlags.mockResolvedValue(mockFlags);
    mockIsAuthenticated.mockResolvedValue(mockUserSession);

    const result = await fetchFlags(mockRequest);

    expect(mockIsAuthenticated).toHaveBeenCalledWith(mockRequest);
    expect(mockConfigurationParameters).toHaveBeenCalled();
    expect(mockFlagsFlags).toHaveBeenCalled();
    expect(result).toEqual(mockFlags);
  });

  it("should fetch flags for unauthenticated user with new API", async () => {
    mockFlagsFlags.mockResolvedValue(mockFlags);
    mockIsAuthenticated.mockResolvedValue(null);

    const result = await fetchFlags(mockRequest);

    expect(mockIsAuthenticated).toHaveBeenCalledWith(mockRequest);
    expect(mockNonAuthenticatedConfigurationParameters).toHaveBeenCalled();
    expect(mockFlagsFlags).toHaveBeenCalled();
    expect(result).toEqual(mockFlags);
  });

  it("should return default flags when new API fetch fails", async () => {
    const mockError = new Error("API fetch failed");
    mockFlagsFlags.mockRejectedValue(mockError);
    mockIsAuthenticated.mockResolvedValue(mockUserSession);

    const result = await fetchFlags(mockRequest);

    expect(mockLogError).toHaveBeenCalledWith("!!! users/flags", mockError);
    expect(result).toEqual({ flags: {}, switches: {}, samples: {} });
  });

  it("should return default flags when new API authentication check fails", async () => {
    const mockError = new Error("Auth failed");
    mockIsAuthenticated.mockRejectedValue(mockError);

    const result = await fetchFlags(mockRequest);

    expect(mockFlagsFlags).not.toHaveBeenCalled();
    expect(mockLogError).toHaveBeenCalledWith("!!! users/flags", mockError);
    expect(result).toEqual({ flags: {}, switches: {}, samples: {} });
  });

  it("should handle malformed response data from new API", async () => {
    mockFlagsFlags.mockResolvedValue("invalid data" as any);
    mockIsAuthenticated.mockResolvedValue(mockUserSession);

    const result = await fetchFlags(mockRequest);

    expect(mockFlagsFlags).toHaveBeenCalled();
    expect(mockLogError).toHaveBeenCalled();
    expect(result).toEqual({ flags: {}, switches: {}, samples: {} });
  });

  it("should handle null response from new API", async () => {
    mockFlagsFlags.mockResolvedValue(null as any);
    mockIsAuthenticated.mockResolvedValue(mockUserSession);

    const result = await fetchFlags(mockRequest);

    expect(mockFlagsFlags).toHaveBeenCalled();
    expect(mockLogError).toHaveBeenCalled();
    expect(result).toEqual({ flags: {}, switches: {}, samples: {} });
  });

  it("should handle empty flags response from new API", async () => {
    const emptyFlags = { flags: {}, switches: {}, samples: {} };
    mockFlagsFlags.mockResolvedValue(emptyFlags);
    mockIsAuthenticated.mockResolvedValue(mockUserSession);

    const result = await fetchFlags(mockRequest);

    expect(mockFlagsFlags).toHaveBeenCalled();
    expect(result).toEqual(emptyFlags);
  });
});
