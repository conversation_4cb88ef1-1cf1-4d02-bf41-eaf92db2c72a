import { Flags, FlagsStruct } from "~/context/types";
import { logError } from "~/utils/log.server";
import { authenticator } from "~/auth/authenticator.server";
import { Configuration, FlagsApi } from "../openapi/generated";
import {
  configurationParameters,
  nonAuthenticatedConfigurationParameters,
} from "../openapi/configParams";

export const fetchFlags = async (request: Request): Promise<Flags> => {
  try {
    const isAuthenticated =
      (await authenticator.isAuthenticated(request)) !== null;
    const configuration = new Configuration(
      isAuthenticated
        ? await configurationParameters(request)
        : await nonAuthenticatedConfigurationParameters()
    );
    return FlagsStruct.parse(await new FlagsApi(configuration).flagsFlags());
  } catch (error) {
    logError("!!! users/flags", error);
    return Promise.resolve({ flags: {}, switches: {}, samples: {} });
  }
};
