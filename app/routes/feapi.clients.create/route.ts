// API URL: /feapi/clients/create
import { ActionFunctionArgs } from "react-router";

import { configurationParameters } from "~/api/openapi/configParams";
import {
  ClientApi,
  Configuration,
  CreateClientRequest,
} from "~/api/openapi/generated";
import isApiError from "~/utils/isApiError";
import { logError } from "~/utils/log.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const {
      name,
      jobTitle,
      email,
      phoneNumber,
      dateOfBirth,
      firstName,
      lastName,
      onboardingDate,
      type,
    } = body;

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const clientApi = new ClientApi(configuration);

    const createClientRequest: CreateClientRequest = {
      name,
      jobTitle,
      email,
      onboardingDate: onboardingDate ? new Date(onboardingDate) : null,
      type,
      dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
      firstName,
      lastName,
    };
    phoneNumber && (createClientRequest.phoneNumber = phoneNumber);

    const response = await clientApi.clientCreateClient({
      createClientRequest,
    });

    return {
      success: true,
      clientUuid: response.clientUuid,
    };
  } catch (e: unknown) {
    logError("e", e);

    let errorMsg = "Failed to create a client";

    if (isApiError(e)) {
      if (e.response.status === 400) {
        errorMsg = "Client with the same email already exists";
      }
    }

    return { success: false, errorMsg };
  }
};
