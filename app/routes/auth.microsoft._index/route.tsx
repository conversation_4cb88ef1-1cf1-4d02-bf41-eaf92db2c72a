import {
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
  redirect,
} from "react-router";
import {
  MICROSOFT_STRATEGY_SKIP_ACCOUNT_SELECTOR,
  MICROSOFT_STRATEGY,
  authenticator,
} from "~/auth/authenticator.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  if (searchParams.get("prompt") === "select_account") {
    return authenticator.authenticate(MICROSOFT_STRATEGY, request);
  }
  return redirect("/auth/login");
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const autoRedirect =
    process.env.ZEPLYN_ENABLE_MICROSOFT_LOGIN_AUTO_REDIRECT === "true";
  if (autoRedirect) {
    return authenticator.authenticate(
      MICROSOFT_STRATEGY_SKIP_ACCOUNT_SELECTOR,
      request
    );
  }
  return authenticator.authenticate(MICROSOFT_STRATEGY, request);
};
