import { SearchIcon } from "lucide-react";

import { MultiSelect } from "~/@ui/MultiSelect";
import { ProcessingStatus } from "~/api/openapi/generated";

type Props = {
  searchText: string;
  setSearchText: (searchText: string) => void;
  statuses: ProcessingStatus[];
  setStatuses: React.Dispatch<React.SetStateAction<ProcessingStatus[]>>;
  selectedAttendees: string[];
  setSelectedAttendees: React.Dispatch<React.SetStateAction<string[]>>;
  statusOptions: { label: string; value: ProcessingStatus }[];
  clientOptions: { label: string; value: string }[];
};
const Search = ({
  searchText,
  setSearchText,
  statuses,
  setStatuses,
  selectedAttendees,
  setSelectedAttendees,
  statusOptions,
  clientOptions,
}: Props) => {
  return (
    <div className="my-4 flex w-full max-w-[1100px] items-center gap-2 rounded-lg border shadow-lg focus-within:border-primary">
      <input
        type="text"
        placeholder="Type name or keywords to search…"
        className="grow rounded-lg p-4 outline-none"
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        autoFocus
      />

      <MultiSelect
        options={statusOptions}
        selected={statuses}
        onChange={(statuses) => setStatuses(statuses as ProcessingStatus[])}
        placeholder="Select Status"
        triggerClassName="shadow-none border-none w-fit"
        useLabelForSearch
      />

      <MultiSelect
        options={clientOptions}
        selected={selectedAttendees}
        onChange={setSelectedAttendees}
        placeholder="Select Attendees"
        triggerClassName="shadow-none border-none w-fit"
      />

      <button className="inline-flex h-full items-center gap-2 rounded-r-lg bg-primary px-5 text-primary-foreground">
        <SearchIcon size={18} />
      </button>
    </div>
  );
};

export default Search;
