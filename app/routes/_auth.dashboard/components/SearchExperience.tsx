import { useEffect, useState } from "react";

import SearchBar from "./SearchBar";
import SearchResults from "./SearchResults";
import {
  ListNotesResponse,
  ProcessingStatus,
  ScheduledEvent,
} from "~/api/openapi/generated";
import { CombinedEvent, combineEvents } from "~/utils/notesUtils";

type Props = {
  isShowingSearchResults: boolean;
  setIsShowingSearchResults: (isShowingSearchResults: boolean) => void;
  notes: ListNotesResponse[];
  crmSystem: string | null | undefined;
  handleSync: (noteId: string) => void;
  openDeleteModal: (noteId: string) => void;
  calendarEventsPromise: Promise<ScheduledEvent[] | undefined>;
};

const SearchExperience = ({
  isShowingSearchResults,
  setIsShowingSearchResults,
  notes,
  crmSystem,
  handleSync,
  openDeleteModal,
  calendarEventsPromise,
}: Props) => {
  const [searchText, setSearchText] = useState("");
  const [statuses, setStatuses] = useState<ProcessingStatus[]>([]);
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);

  const [calendarEvents, setCalendarEvents] = useState<CombinedEvent[]>([]);

  useEffect(() => {
    calendarEventsPromise.then((calendarEvents) => {
      if (!calendarEvents) {
        return;
      }
      const events = combineEvents(calendarEvents, [], [], new Set());
      setCalendarEvents(events);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // show search results if any of the dependencies is available; else hide
  useEffect(() => {
    if (searchText || statuses.length || selectedAttendees.length) {
      setIsShowingSearchResults(true);
    } else {
      setIsShowingSearchResults(false);
    }
  }, [searchText, statuses, selectedAttendees, setIsShowingSearchResults]);

  const clientOptions = Array.from(
    new Set(notes.map((note) => note.client?.name).filter(Boolean))
  ).map((clientName) => ({
    label: clientName!,
    value: clientName!,
  }));

  const onClearSearch = () => {
    setSearchText("");
    setStatuses([]);
    setSelectedAttendees([]);
  };

  const { filteredCalendarEvents, filteredNotes } = getFilteredNotes(
    notes,
    calendarEvents,
    searchText,
    statuses,
    selectedAttendees
  );

  return (
    <>
      <SearchBar
        searchText={searchText}
        statuses={statuses}
        setStatuses={setStatuses}
        setSearchText={setSearchText}
        selectedAttendees={selectedAttendees}
        setSelectedAttendees={setSelectedAttendees}
        statusOptions={statusOptions}
        clientOptions={clientOptions}
      />

      {isShowingSearchResults && (
        <SearchResults
          filteredCalendarEvents={filteredCalendarEvents}
          filteredNotes={filteredNotes}
          onReset={onClearSearch}
          crmSystem={crmSystem}
          handleSync={handleSync}
          openDeleteModal={openDeleteModal}
        />
      )}
    </>
  );
};

function getFilteredNotes(
  notes: ListNotesResponse[],
  calendarEvents: CombinedEvent[],
  searchText: string,
  statuses: ProcessingStatus[],
  selectedAttendees: string[]
) {
  const alteredSearchText = searchText.toLowerCase().trim();

  const filteredCalendarEvents = calendarEvents.filter((event) => {
    const searchQueryMatch =
      !alteredSearchText ||
      event.meetingName.toLowerCase().includes(alteredSearchText) ||
      event.attendees?.some((attendee) =>
        attendee.name.toLowerCase().includes(alteredSearchText)
      );

    const attendeeMatch =
      selectedAttendees.length === 0 ||
      event.attendees?.some((attendee) =>
        selectedAttendees.includes(attendee.name)
      );

    return searchQueryMatch && attendeeMatch;
  });

  const filteredNotes = notes.filter((note) => {
    // search based on meeting name, keywords, attendees
    const searchQueryMatch =
      !alteredSearchText ||
      note.meetingName.toLowerCase().includes(alteredSearchText) ||
      note.tags.some((tag) => tag.toLowerCase().includes(alteredSearchText)) ||
      note.attendees?.some((attendee) =>
        attendee.name.toLowerCase().includes(alteredSearchText)
      );

    const statusMatch = statuses.length === 0 || statuses.includes(note.status);

    const attendeeMatch =
      selectedAttendees.length === 0 ||
      note.attendees?.some((attendee) =>
        selectedAttendees.includes(attendee.name)
      );

    return searchQueryMatch && statusMatch && attendeeMatch;
  });

  return { filteredCalendarEvents, filteredNotes };
}

const statusOptions = [
  {
    label: "Processing",
    value: ProcessingStatus.Uploaded,
  },
  {
    label: "Ready for review",
    value: ProcessingStatus.Processed,
  },
  {
    label: "Synced with CRM",
    value: ProcessingStatus.Finalized,
  },
];

export default SearchExperience;
