import { Mic } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import { Link } from "react-router";
import { <PERSON><PERSON> } from "~/@shadcn/ui/button";
import { GoogleMeetIcon } from "~/@ui/assets/GoogleMeetIcon";
import { MicrosoftTeamsIcon } from "~/@ui/assets/MicrosoftTeamsIcon";
import { WebexIcon } from "~/@ui/assets/WebexIcon";
import { ZoomIcon } from "~/@ui/assets/ZoomIcon";

const iconForMeetingLink = (meetingLink: string | undefined) => {
  if (!meetingLink) return undefined;
  let host: string | undefined;
  try {
    host = new URL(meetingLink).host;
  } catch {
    return undefined;
  }
  // Google Meet: meet.google.com or subdomains
  if (host === "google.com" || host.endsWith(".google.com"))
    return GoogleMeetIcon;
  // Zoom: zoom.us or subdomains
  if (host === "zoom.us" || host.endsWith(".zoom.us")) return ZoomIcon;
  // Microsoft Teams: teams.microsoft.com or subdomains
  if (host === "teams.microsoft.com" || host.endsWith(".teams.microsoft.com"))
    return MicrosoftTeamsIcon;
  // Webex: webex.com or subdomains
  if (host === "webex.com" || host.endsWith(".webex.com")) return WebexIcon;
  return undefined;
};

const MeetingLinkButton = ({
  meetingLink,
  shouldLinkToMeeting,
}: {
  meetingLink: string;
  shouldLinkToMeeting?: boolean;
}) => {
  const IconComponent = iconForMeetingLink(meetingLink);
  if (!IconComponent) {
    return null;
  }

  return shouldLinkToMeeting && meetingLink ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="outline_primary" size="default" asChild>
            <Link to={meetingLink} target="_blank">
              <IconComponent className="!h-5 !w-5" />
              Join Meeting
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Join meeting</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <IconComponent className="h-6 w-6" />
  );
};

export default MeetingLinkButton;
