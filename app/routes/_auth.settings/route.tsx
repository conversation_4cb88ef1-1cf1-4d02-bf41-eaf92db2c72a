import { useEffect, useLayoutEffect } from "react";
import {
  ActionFunctionArgs,
  LoaderFunctionArgs,
  MetaFunction,
} from "react-router";
import { Outlet, useLoaderData, useLocation, useNavigate } from "react-router";

import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import {
  Configuration,
  MenuItem,
  PreferencesApi,
  SettingsApi,
} from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import { ContentV2, HeaderV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { cn } from "~/@shadcn/utils";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";

import SettingsMenu from "./components/SettingsMenu";
import UserProfile from "./components/UserProfile";
import { Steps } from "intro.js-react";
import useOnboarding from "~/utils/useOnboarding";
import { tutorialSteps } from "./utils";
import { toast } from "react-toastify";

export const meta: MetaFunction = () => {
  return [
    { title: "Settings" },
    { name: "description", content: "View and update Zeplyn settings" },
  ];
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const config = new Configuration(await configurationParameters(request));

  const preferencesPromise = new PreferencesApi(
    config
  ).preferencesGetPreferenceSchemas();

  const settingsMenuPromise = new SettingsApi(
    config
  ).settingsGetSettingsMenuRoute();

  const userDataPromise = getUserSessionOrRedirect(request);

  const [{ firstName, lastName, email }, settingsMenuData, preferences] =
    await Promise.all([
      userDataPromise,
      settingsMenuPromise,
      preferencesPromise,
    ]);

  return {
    firstName,
    lastName,
    email,
    settingsMenuData,
    preferences,
    firstRoute: getFirstRelativeLink(settingsMenuData),
  };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const settingsData = JSON.parse(formData.get("settings-data") as string);
  const config = new Configuration(await configurationParameters(request));
  try {
    await new SettingsApi(config).settingsSave({
      saveRequest: settingsData,
    });
    return { success: true };
  } catch (e) {
    return { success: false };
  }
};

const BASE_ROUTE = "settings";

// redirect to the first "relative" link in the settings menu if user visits base route on desktop device
const useRedirectToFirstRelativeLink = (
  url: string,
  isOnBaseRoute: boolean
) => {
  const navigate = useNavigate();
  const { matchedBreakpoints } = useTailwindBreakpoints();
  const isDesktop = matchedBreakpoints.has("lg");

  // TODO: @debojyotighosh Consider replacing with useEffect; implement & make use of `isMobile`
  useLayoutEffect(() => {
    if (isDesktop && url && isOnBaseRoute) {
      navigate(`/${BASE_ROUTE}${url}`, { replace: true });
    }
  }, [navigate, isDesktop, url, isOnBaseRoute]);
};

const Route = () => {
  const {
    firstName,
    lastName,
    email,
    settingsMenuData,
    preferences,
    firstRoute,
  } = useLoaderData<typeof loader>();
  const { isTutorialEnabled, completeTutorial } = useOnboarding("settings", {
    triggerViaUrl: true,
  });

  const location = useLocation();
  const navigate = useNavigate();
  const isOnBaseRoute = checkIfOnBaseRoute(location.pathname);

  useRedirectToFirstRelativeLink(firstRoute, isOnBaseRoute);

  const searchParams = new URLSearchParams(location.search);
  const type = searchParams.get("type");
  const status = searchParams.get("status");
  const name = searchParams.get("name");

  useEffect(() => {
    if (!status || !name || !type) {
      return;
    }

    const toastFn = status === "true" ? toast.success : toast.error;
    let toastMsg;

    switch (type) {
      case "integration":
        toastMsg =
          status === "true"
            ? `${name} integration successful`
            : `${name} integration failed`;
    }

    toastFn(toastMsg, {
      toastId: "status-toast",
    });
  }, [type, status, name]);

  const onCompleteTutorial = () => {
    completeTutorial();
    navigate("/settings", { replace: true });
  };

  const onExitTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteTutorial();
    }
  };

  return (
    <LayoutV2>
      <ContentV2
        className="justify-start overflow-hidden overscroll-none"
        header={
          <HeaderV2
            className="h-auto border-b border-gray-200 py-0 pl-2"
            left={
              <UserProfile
                firstName={firstName || ""}
                lastName={lastName || ""}
                email={email || ""}
              />
            }
          />
        }
        innerClassName="p-0"
      >
        <Steps
          enabled={isTutorialEnabled}
          steps={tutorialSteps}
          initialStep={0}
          onExit={onExitTutorial}
          onComplete={onCompleteTutorial}
          options={{
            exitOnOverlayClick: false,
          }}
        />

        <div className="flex w-full flex-grow flex-row">
          {/* for smaller devices, hide the menu panel if NOT on base route */}
          <div
            className={cn(
              "h-full p-5 md:block md:border-r md:border-solid md:border-gray-200 md:pr-10 md:pt-1",
              !isOnBaseRoute && "hidden"
            )}
          >
            <SettingsMenu
              menuData={settingsMenuData}
              preferences={preferences}
            />
          </div>

          <div className="w-full p-5 pb-16 md:block md:pl-8">
            <Outlet />
          </div>
        </div>
      </ContentV2>
    </LayoutV2>
  );
};

// returns true if user is on /settings page
function checkIfOnBaseRoute(pathname: string) {
  // compare pathname (without the leading and trailing slashes) to settings base route
  return pathname.replace(/^\/+|\/+$/g, "") === BASE_ROUTE;
}

// returns the first "relative" link from the settings menu data
function getFirstRelativeLink(data: MenuItem[]) {
  for (const section of data) {
    if (!section.items) {
      continue;
    }
    for (const item of section.items) {
      if (item.id) {
        return `/${item.id}`;
      }
    }
  }
  return ""; // if no `id` is found
}

export default Route;
