// API URL: /feapi/preferences/get
import { LoaderFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, PreferencesApi } from "~/api/openapi/generated";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const config = new Configuration(await configurationParameters(request));

  const preferences = await new PreferencesApi(
    config
  ).preferencesGetPreferenceSchemas();

  return preferences;
};
