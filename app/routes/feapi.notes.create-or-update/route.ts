// API URL: /feapi/notes/create-or-update
import { ActionFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, NoteApi } from "~/api/openapi/generated";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const {
      noteId,
      meetingType,
      meetingName,
      attendees,
      meetingLink,
      meetingSourceId,
      scheduledStartTime,
      scheduledEndTime,
      scheduledEventUuid,
      clientName,
      clientId,
    } = body;

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const noteApi = new NoteApi(configuration);

    await noteApi.noteCreateOrUpdateNote({
      noteId,
      meetingType,
      meetingName,
      attendees: JSON.stringify(attendees),
      meetingLink,
      meetingSourceId,
      scheduledStartTime: new Date(scheduledStartTime),
      scheduledEndTime: new Date(scheduledEndTime),
      scheduledEventUuid,
      clientName,
      clientId,
    });

    return { success: true };
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error("Failed to save note", e);
    return { success: false };
  }
};
