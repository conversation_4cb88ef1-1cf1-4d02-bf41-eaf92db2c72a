// API URL: /feapi/notes/generate-meeting-prep
import { ActionFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, NoteApi } from "~/api/openapi/generated";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const { noteId, clientIds } = body;

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const noteApi = new NoteApi(configuration);

    await noteApi.noteGenerateMeetingPrep({
      noteId,
      generateMeetingPrepRequest: {
        clientIds,
      },
    });

    return { success: true };
  } catch (e) {
    return { success: false };
  }
};
