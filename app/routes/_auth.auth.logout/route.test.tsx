import { describe, test, vi, expect, beforeEach } from "vitest";
import { loader } from "./route";
import { authenticator } from "~/auth/authenticator.server";
import { AuthApi } from "~/api/openapi/generated";

vi.mock("~/api/openapi/generated");
vi.mock("~/api/openapi/configParams");

describe("logout loader", () => {
  const redirectResponseMatcher = (expectedURL?: string) => (v: any) =>
    v instanceof Response &&
    v.status === 302 &&
    v.headers.get("location") === (expectedURL ?? "/auth/login");

  beforeEach(() => {
    vi.resetAllMocks();
  });

  const logoutRedirect = (url?: string) => {
    return { status: 302, headers: { location: url ?? "/auth/login" } };
  };

  test("should redirect to /auth/login if not logged in", async () => {
    const request = new Request("http://localhost/auth/logout");
    await expect(
      loader({ request, params: {}, context: {} })
    ).rejects.toSatisfy(redirectResponseMatcher());
  });

  test("should redirect after backend logout", async () => {
    vi.spyOn(authenticator, "isAuthenticated").mockResolvedValue(null);
    vi.spyOn(AuthApi.prototype, "authLogout").mockResolvedValue({});

    const request = new Request("http://localhost/auth/logout");
    await expect(
      loader({ request, params: {}, context: {} })
    ).rejects.toSatisfy(redirectResponseMatcher());

    expect(AuthApi).toHaveBeenCalled();
    const mockAuthApiInstance = vi.mocked(AuthApi).mock.results[0]?.value;
    expect(mockAuthApiInstance?.authLogout).toHaveBeenCalled();
  });

  test("should redirect with redirect path", async () => {
    vi.spyOn(authenticator, "isAuthenticated").mockResolvedValue(null);
    vi.spyOn(AuthApi.prototype, "authLogout").mockResolvedValue({});

    const request = new Request(
      "http://localhost/auth/logout?redirectTo=/test"
    );
    await expect(
      loader({ request, params: {}, context: {} })
    ).rejects.toSatisfy(redirectResponseMatcher("/test"));

    expect(AuthApi).toHaveBeenCalled();
    const mockAuthApiInstance = vi.mocked(AuthApi).mock.results[0]?.value;
    expect(mockAuthApiInstance?.authLogout).toHaveBeenCalled();
  });

  test("should logout even if backend logout fails", async () => {
    vi.spyOn(authenticator, "isAuthenticated").mockResolvedValue(null);
    vi.spyOn(AuthApi.prototype, "authLogout").mockRejectedValue({});

    const request = new Request("http://localhost/auth/logout");
    await expect(
      loader({ request, params: {}, context: {} })
    ).rejects.toSatisfy(redirectResponseMatcher());
  });
});
