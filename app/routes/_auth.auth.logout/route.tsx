import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { AuthApi, Configuration } from "~/api/openapi/generated";
import { authenticator } from "~/auth/authenticator.server";

// Exports
export const meta: MetaFunction = () => [
  { title: "Logout" },
  { name: "description", content: "Logout" },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const redirectTo = url.searchParams.get("redirectTo");

  // If the user is not logged in, redirect to login; there's nothing to do.
  await authenticator.isAuthenticated(request, {
    failureRedirect: "/auth/login",
  });

  try {
    // Log out the user on the backend.
    const config = new Configuration(await configurationParameters(request));
    await new AuthApi(config).authLogout();
  } catch {
    // The user may have credentials that are invalid, and so the logout may fail.
    // eslint-disable-next-line no-console
    console.warn(
      "Error logging out user on backend. This can happen if the user's credentials " +
        "are expired or otherwise invalid, so it's not necessarily an error."
    );
  }

  // Log user out and redirect on page load
  await authenticator.logout(request, {
    redirectTo: redirectTo ?? "/auth/login",
  });
};

const Route = () => {
  // Don't render anything, user should get redirected to /auth/login via loader
  return null;
};
export default Route;
