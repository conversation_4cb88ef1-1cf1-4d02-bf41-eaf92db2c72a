import { LoaderFunctionArgs, redirect } from "react-router";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import { OAuthRequestProviderEnum } from "~/api/openapi/generated";
import { MICROSOFT_STRATEGY, authenticator } from "~/auth/authenticator.server";
import { logError } from "~/utils/log.server";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  if (searchParams.get("state") !== "calendar_integration") {
    const error = searchParams.get("error");
    if (
      process.env.ZEPLYN_ENABLE_MICROSOFT_LOGIN_AUTO_REDIRECT &&
      (error === "interaction_required" || error === "login_required")
    ) {
      return redirect("/auth/microsoft?prompt=select_account");
    }
    await authenticator.authenticate(MICROSOFT_STRATEGY, request, {
      successRedirect: "/",
      failureRedirect: "/auth/login",
    });
    return null;
  }

  const authorizationCode = searchParams.get("code");
  if (!authorizationCode) {
    throw new Error("Missing authorization_code query parameter");
  }

  try {
    await setUpOAuthProvider({
      authorizationCode,
      provider: OAuthRequestProviderEnum.Microsoft,
      request,
    });
    return redirect(
      "/settings/integrations?type=integration&status=true&name=Microsoft Calendar"
    );
  } catch (error) {
    logError("Failed to setup calendar integration", error);
    return redirect(
      "/settings/integrations?type=integration&status=false&name=Microsoft Calendar"
    );
  }
};
