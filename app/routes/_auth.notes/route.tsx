import { useState, useEffect, useRef } from "react";
import { data, redirect, type LoaderFunctionArgs } from "react-router";
import {
  NavLink,
  Outlet,
  useLoaderData,
  useLocation,
  useOutletContext,
} from "react-router";
import { getNotes } from "~/api/notes/getNotes.server";
import { Typography } from "~/@ui/Typography";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { NoteListCard } from "~/@ui/notes/NoteListCard";
import { Fab } from "~/@ui/Fab";
import { subDays, isAfter, addHours } from "date-fns";
import { MultiSelect } from "~/@ui/MultiSelect";
import { Button } from "~/@shadcn/ui/button";
import { ListNotesResponse, ProcessingStatus } from "~/api/openapi/generated";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  ListFilterIcon,
  Plus,
  X,
} from "lucide-react";
import { datadogLogs } from "@datadog/browser-logs";
import { useFlag } from "~/context/flags";

const statusOptions = [
  {
    label: "Processing",
    value: ProcessingStatus.Uploaded,
  },
  {
    label: "Ready for review",
    value: ProcessingStatus.Processed,
  },
  {
    label: "Synced with CRM",
    value: ProcessingStatus.Finalized,
  },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchTerm =
    new URL(request.url).searchParams.get("searchTerm") ?? undefined;

  const notesData = await getNotes({ searchTerm, request }).catch((error) => {
    if (error.response?.status === 401) {
      throw redirect("/auth/logout");
    }
    return "Error fetching notes";
  });

  return data({
    notesOrErrorString: notesData,
    searchTerm,
  });
};

const Route = () => {
  const { notesOrErrorString, searchTerm } = useLoaderData<typeof loader>();

  const location = useLocation();
  const [showFilter, setShowFilter] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(true);
  const [loading, setLoading] = useState(true);

  // Local state for filter inputs
  const [status, setStatus] = useState<ProcessingStatus[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);

  // State to store applied filters
  const [appliedStatus, setAppliedStatus] = useState<string[]>([]);
  const [appliedSearchQuery, setAppliedSearchQuery] = useState<string>("");
  const [appliedClients, setAppliedClients] = useState<string[]>([]);

  const isUnifyAdvisorHubAndNotesEnabled = useFlag(
    "EnableUnifyAdvisorHubAndNotes"
  );

  const filterRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const notes = notesOrErrorString instanceof Array ? notesOrErrorString : [];
  const notesError =
    notesOrErrorString instanceof Array ? undefined : notesOrErrorString;

  useEffect(() => {
    const savedVisibility = localStorage.getItem("isContentVisible");
    if (savedVisibility !== null) {
      setIsContentVisible(JSON.parse(savedVisibility));
    }
    setLoading(false);
  }, []);

  const handleVisibilityToggle = () => {
    setIsContentVisible((prev) => {
      const newState = !prev;
      localStorage.setItem("isContentVisible", JSON.stringify(newState));
      return newState;
    });
  };

  const toggleFilter = () => {
    setShowFilter(!showFilter);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowFilter(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleApplyFilters = () => {
    setAppliedStatus(status);
    setAppliedClients(selectedClients);
    setShowFilter(false);
  };
  const handleClearSearch = () => {
    setAppliedSearchQuery("");
  };

  const handleClearFilters = () => {
    setStatus([]);
    setSelectedClients([]);
    setAppliedStatus([]);
    setAppliedClients([]);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleApplyFilters();
    }
  };

  const groupNotesByTime = (notes: ListNotesResponse[]) => {
    const thisWeek: ListNotesResponse[] = [];
    const lastWeek: ListNotesResponse[] = [];
    const recent: ListNotesResponse[] = [];
    const older: ListNotesResponse[] = [];

    const now = new Date();
    const startOfThisWeek = subDays(now, now.getDay());
    const startOfLastWeek = subDays(startOfThisWeek, 7);
    const oneMonthAgo = subDays(now, 30);

    notes.forEach((note) => {
      // TODO: always use the note's scheduled end time
      const referenceDate =
        note.status == ProcessingStatus.Scheduled
          ? note.scheduledEndTime
            ? new Date(note.scheduledEndTime)
            : addHours(new Date(note.scheduledStartTime ?? note.created), 1)
          : new Date(note.created);

      // Skip future notes; they are displayed in the advisor hub.
      if (referenceDate > now) {
        return;
      }

      if (isAfter(referenceDate, startOfThisWeek)) {
        thisWeek.push(note);
      } else if (isAfter(referenceDate, startOfLastWeek)) {
        lastWeek.push(note);
      } else if (isAfter(referenceDate, oneMonthAgo)) {
        recent.push(note);
      } else {
        older.push(note);
      }
    });

    return { thisWeek, lastWeek, recent, older };
  };

  const renderNoteSection = (title: string, noteList: ListNotesResponse[]) => {
    if (noteList.length === 0) return null;

    return (
      <div className="w-full overflow-x-auto">
        <Typography variant="h6" className="mb-2 text-gray-700">
          {title}
        </Typography>
        <div className="w-full overflow-hidden rounded-xl border">
          {noteList.map((note) => (
            <NoteListCard
              key={note.uuid}
              type={note.noteType}
              status={note.status}
              meetingName={note.meetingName}
              created={note.created}
              scheduledStartTime={note.scheduledStartTime}
              to={
                note.status === ProcessingStatus.Scheduled
                  ? {
                      pathname: `/notes/create/${note.uuid}`,
                      search: `?noteID=${note.uuid}`,
                    }
                  : {
                      pathname: `/notes/${note.uuid}`,
                      search: searchTerm
                        ? `?searchTerm=${searchTerm}`
                        : undefined,
                    }
              }
            />
          ))}
        </div>
      </div>
    );
  };

  const filteredNotes = notes.filter((note) => {
    const statusMatch =
      appliedStatus.length === 0 || appliedStatus.includes(note.status);
    const searchQueryMatch =
      !appliedSearchQuery ||
      note.meetingName
        .toLowerCase()
        .includes(appliedSearchQuery.toLowerCase()) ||
      note.tags.some((tag) =>
        tag.toLowerCase().includes(appliedSearchQuery.toLowerCase())
      ) ||
      note.attendees?.some((attendee) =>
        attendee.name.toLowerCase().includes(appliedSearchQuery.toLowerCase())
      );

    const clientMatch =
      appliedClients.length === 0 ||
      (note.client?.name && appliedClients.includes(note.client.name));

    return statusMatch && searchQueryMatch && clientMatch;
  });

  const groupedNotes = groupNotesByTime(filteredNotes);

  const clientOptions = Array.from(
    new Set(notes.map((note) => note.client?.name).filter(Boolean))
  ).map((clientName) => ({
    label: clientName!,
    value: clientName!,
  }));

  if (loading) {
    return null;
  }

  if (isUnifyAdvisorHubAndNotesEnabled) {
    return (
      <LayoutV2>
        <Outlet key={location.pathname} context={{ notes }} />
      </LayoutV2>
    );
  }

  return (
    <LayoutV2>
      <ContentV2
        floatingAction={
          <Fab asChild>
            <NavLink to="/notes/create">
              <Plus />
            </NavLink>
          </Fab>
        }
        className="w-80 min-w-80"
      >
        <div className="flex w-full items-center justify-between">
          <Button variant="outline" onClick={handleVisibilityToggle}>
            {isContentVisible ? <ChevronUpIcon /> : <ChevronDownIcon />}
            <span>{isContentVisible ? "Hide" : "Show"} Notes</span>
          </Button>
          {isContentVisible && (
            <button
              ref={buttonRef}
              className="flex items-center gap-1 p-2 hover:text-blue-500"
              onClick={toggleFilter}
            >
              <ListFilterIcon className="!h-5 !w-5" />
              <span>Filter</span>
              {showFilter ? (
                <ChevronUpIcon className="!h-3 !w-3" />
              ) : (
                <ChevronDownIcon className="!h-3 !w-3" />
              )}
            </button>
          )}
        </div>

        {isContentVisible && (
          <>
            <div className="relative flex w-full items-center justify-end gap-4 text-gray-500">
              {showFilter && (
                <div
                  ref={filterRef}
                  className="absolute left-0 top-0 z-50 w-full rounded-md border border-gray-200 bg-white/75 p-4 shadow-lg backdrop-blur-sm"
                >
                  <div className="flex flex-col space-y-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-500">
                        Status
                      </label>
                      <MultiSelect
                        options={statusOptions}
                        selected={status}
                        onChange={(statuses) =>
                          setStatus(statuses as ProcessingStatus[])
                        }
                        placeholder="Select status"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-500">
                        Client
                      </label>
                      <MultiSelect
                        options={clientOptions}
                        selected={selectedClients}
                        onChange={setSelectedClients}
                        placeholder="Select client"
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <button
                        className="rounded bg-blue-500 px-4 py-2 text-white"
                        onClick={handleApplyFilters}
                      >
                        Apply Filters
                      </button>
                      <button
                        className="rounded bg-gray-300 px-4 py-2 text-black"
                        onClick={handleClearFilters}
                      >
                        Clear Filters
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="flex w-full flex-col">
              <label className="block text-xs font-medium text-gray-500">
                Search by Name or Keywords
              </label>
              <div className="relative w-full">
                <input
                  type="text"
                  placeholder="Search by name or keywords..."
                  data-onboarding="search-notes"
                  value={appliedSearchQuery}
                  onChange={(e) => setAppliedSearchQuery(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="block w-full rounded border border-gray-300 p-2 shadow-sm"
                />
                {appliedSearchQuery && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-2 top-1/2 -translate-y-1/2 transform"
                  >
                    <X size={20} />
                  </button>
                )}
              </div>
            </div>
            <div className="flex w-full flex-col gap-4">
              {notesError && (
                <Typography variant="h6" color="error">
                  {notesError}
                </Typography>
              )}
              {renderNoteSection("This Week", groupedNotes.thisWeek)}
              {renderNoteSection("Last Week", groupedNotes.lastWeek)}
              {renderNoteSection("Last 30 days", groupedNotes.recent)}
              {renderNoteSection("Older", groupedNotes.older)}
            </div>
          </>
        )}
      </ContentV2>

      <Outlet key={location.pathname} context={{ notes }} />
    </LayoutV2>
  );
};

export default Route;

// A type for the notes that are passed through the outlet context.
type NotesContextType = { notes: ListNotesResponse[] };

// A hook that provies typed access to the notes passed in the outlet context.
export function useNotesFromContext() {
  return useOutletContext<NotesContextType>();
}
