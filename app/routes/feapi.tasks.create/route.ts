// API URL: /feapi/tasks/create
import { ActionFunctionArgs } from "react-router";

import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const { parentNoteUuid, title } = body;

    if (!title) {
      throw new Error("Title is required");
    }

    const userSession = await getUserSessionOrRedirect(request);

    const configuration = new Configuration(
      await configurationParameters(request)
    );

    const taskApi = new TaskApi(configuration);

    const { taskUuid } = await taskApi.taskCreateTask({
      createTaskRequest: {
        title,
        ownerUuid: userSession.userId,
        parentNoteUuid,
      },
    });

    return { success: true, taskUuid };
  } catch (e) {
    return { success: false };
  }
};
