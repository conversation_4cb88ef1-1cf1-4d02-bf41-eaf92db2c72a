// @vitest-environment node

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { action } from "./route";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { v4 as uuidv4 } from "uuid";

// Mock dependencies
vi.mock("~/auth/authenticator.server");
vi.mock("~/api/openapi/configParams");
vi.mock("~/api/openapi/generated");

describe("action", () => {
  const mockUserSession = {
    accessToken: "test-access-token",
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "User",
    userId: uuidv4(),
    orgId: uuidv4(),
  };

  const mockConfigParams = {
    basePath: "http://localhost:8000",
    middleware: [],
  };

  const mockTaskUuid = uuidv4();
  const mockTaskCreateTask = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock getUserSessionOrRedirect
    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(mockUserSession);

    // Mock configurationParameters
    vi.mocked(configurationParameters).mockResolvedValue(mockConfigParams);

    // Mock TaskApi
    mockTaskCreateTask.mockResolvedValue({ taskUuid: mockTaskUuid });
    vi.mocked(TaskApi).mockImplementation(
      () =>
        ({
          taskCreateTask: mockTaskCreateTask,
        }) as any
    );

    // Mock Configuration
    vi.mocked(Configuration).mockImplementation((params) => params as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should create a task successfully with required fields", async () => {
    const requestBody = {
      title: "Test Task",
      parentNoteUuid: uuidv4(),
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: true,
      taskUuid: mockTaskUuid,
    });

    expect(getUserSessionOrRedirect).toHaveBeenCalledWith(request);
    expect(configurationParameters).toHaveBeenCalledWith(request);
    expect(Configuration).toHaveBeenCalledWith(mockConfigParams);
    expect(TaskApi).toHaveBeenCalledWith(mockConfigParams);
    expect(mockTaskCreateTask).toHaveBeenCalledWith({
      createTaskRequest: {
        title: requestBody.title,
        ownerUuid: mockUserSession.userId,
        parentNoteUuid: requestBody.parentNoteUuid,
      },
    });
  });

  it("should create a task successfully without parentNoteUuid", async () => {
    const requestBody = {
      title: "Test Task Without Parent",
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: true,
      taskUuid: mockTaskUuid,
    });

    expect(mockTaskCreateTask).toHaveBeenCalledWith({
      createTaskRequest: {
        title: requestBody.title,
        ownerUuid: mockUserSession.userId,
        parentNoteUuid: undefined,
      },
    });
  });

  it("should return error when title is missing", async () => {
    const requestBody = {
      parentNoteUuid: uuidv4(),
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskCreateTask).not.toHaveBeenCalled();
  });

  it("should return error when title is empty string", async () => {
    const requestBody = {
      title: "",
      parentNoteUuid: uuidv4(),
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskCreateTask).not.toHaveBeenCalled();
  });

  it("should return error when getUserSessionOrRedirect throws", async () => {
    vi.mocked(getUserSessionOrRedirect).mockRejectedValue(
      new Error("Authentication failed")
    );

    const requestBody = {
      title: "Test Task",
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskCreateTask).not.toHaveBeenCalled();
  });

  it("should return error when configurationParameters throws", async () => {
    vi.mocked(configurationParameters).mockRejectedValue(
      new Error("Configuration failed")
    );

    const requestBody = {
      title: "Test Task",
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskCreateTask).not.toHaveBeenCalled();
  });

  it("should return error when TaskApi.taskCreateTask throws", async () => {
    mockTaskCreateTask.mockRejectedValue(new Error("API call failed"));

    const requestBody = {
      title: "Test Task",
    };

    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskCreateTask).toHaveBeenCalled();
  });

  it("should return error when request body is invalid JSON", async () => {
    const request = new Request("http://localhost:3000/feapi/tasks/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: "invalid json",
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(getUserSessionOrRedirect).not.toHaveBeenCalled();
    expect(mockTaskCreateTask).not.toHaveBeenCalled();
  });
});
