import { SerializeFrom } from "~/types/remix";
import { v4 as uuidv4 } from "uuid";
import { AttendeeOptions } from "~/api/attendees/types";
import {
  ApiRoutersCrmClientResponse,
  ClientType,
  EditNoteRequest,
  NoteResponse,
} from "~/api/openapi/generated";

// Types
type EditableKeyTakeaway = {
  uuid: string;
  value: string;
  autoFocus: boolean;
};
type EditableKeyTakeawayAction =
  | { type: "updateKeyTakeaway"; uuid: string; nextValue: string }
  | { type: "removeKeyTakeaway"; uuid: string }
  | { type: "addKeyTakeaway" };

type EditableAdvisorNote = {
  uuid: string;
  value: string;
  autoFocus: boolean;
};
type EditableAdvisorNoteAction =
  | { type: "updateAdvisorNote"; uuid: string; nextValue: string }
  | { type: "removeAdvisorNote"; uuid: string }
  | { type: "addAdvisorNote" };

type EditableActionItem = {
  uuid: string;
  checked: boolean;
  value: string;
  autoFocus: boolean;
  action: "keep" | "delete" | "create";
};
type EditableActionItemAction =
  | { type: "updateActionItem"; uuid: string; nextValue: string }
  | { type: "setActionItemChecked"; uuid: string; checked: boolean }
  | { type: "removeActionItem"; uuid: string }
  | { type: "addActionItem" };

type EditableSummaryNode = {
  uuid: string;
  value: string;
  autoFocus: boolean;
};
type EditableSummarySection = {
  uuid: string;
  value: string;
  autoFocus: boolean;
  nodes: EditableSummaryNode[];
};
type EditableSummaryAction =
  | { type: "updateSummarySection"; uuid: string; nextValue: string }
  | { type: "removeSummarySection"; uuid: string }
  | { type: "addSummarySection" }
  | {
      type: "updateSummaryNodeInParentSection";
      parentUuid: string;
      uuid: string;
      nextValue: string;
    }
  | {
      type: "removeSummaryNodeFromParentSection";
      parentUuid: string;
      uuid: string;
    }
  | {
      type: "addSummaryNodeToParentSection";
      parentUuid: string;
    };

type UpdateClientAction = {
  type: "updateClient";
  client?: ApiRoutersCrmClientResponse;
};
type UpdateMeetingNameAction = {
  type: "updateMeetingName";
  nextMeetingName: string;
};
type ResetStateAction = {
  type: "replaceState";
  newState: EditableNoteState;
};
type UpdateAttendeesAction = {
  type: "updateAttendees";
  nextAttendees: AttendeeOptions;
};
// Exports
export type EditableNoteState = {
  touched: boolean;
  meetingName: string;
  client?: ApiRoutersCrmClientResponse;
  keyTakeaways: EditableKeyTakeaway[];
  advisorNotes: EditableAdvisorNote[];
  actionItems: EditableActionItem[];
  summaryByTopics: EditableSummarySection[];
  attendees: AttendeeOptions;
};
export type EditableNoteActions =
  | EditableKeyTakeawayAction
  | EditableAdvisorNoteAction
  | EditableActionItemAction
  | EditableSummaryAction
  | UpdateClientAction
  | UpdateMeetingNameAction
  | UpdateAttendeesAction
  | ResetStateAction;
export const editableNoteReducer = (
  state: EditableNoteState,
  action: EditableNoteActions
): EditableNoteState => {
  switch (action.type) {
    case "updateKeyTakeaway": {
      return {
        ...state,
        touched: true,
        keyTakeaways: state.keyTakeaways.map((item) => {
          if (item.uuid !== action.uuid) return item;
          return {
            ...item,
            value: action.nextValue,
          };
        }),
      };
    }

    case "removeKeyTakeaway": {
      return {
        ...state,
        touched: true,
        keyTakeaways: state.keyTakeaways.filter(
          (item) => item.uuid !== action.uuid
        ),
      };
    }

    case "addKeyTakeaway": {
      return {
        ...state,
        touched: true,
        keyTakeaways: state.keyTakeaways
          .map((item) => ({
            ...item,
            autoFocus: false,
          }))
          .concat({
            uuid: uuidv4(),
            value: "",
            autoFocus: true,
          }),
      };
    }

    case "updateAdvisorNote": {
      return {
        ...state,
        touched: true,
        advisorNotes: state.advisorNotes.map((item) => {
          if (item.uuid !== action.uuid) return item;
          return {
            ...item,
            value: action.nextValue,
          };
        }),
      };
    }

    case "removeAdvisorNote": {
      return {
        ...state,
        touched: true,
        advisorNotes: state.advisorNotes.filter(
          (item) => item.uuid !== action.uuid
        ),
      };
    }

    case "addAdvisorNote": {
      return {
        ...state,
        touched: true,
        advisorNotes: state.advisorNotes
          .map((item) => ({
            ...item,
            autoFocus: false,
          }))
          .concat({
            uuid: uuidv4(),
            value: "",
            autoFocus: true,
          }),
      };
    }

    case "updateActionItem": {
      return {
        ...state,
        touched: true,
        actionItems: state.actionItems.map((item) => {
          if (item.uuid !== action.uuid) return item;
          return {
            ...item,
            value: action.nextValue,
          };
        }),
      };
    }

    case "setActionItemChecked": {
      return {
        ...state,
        touched: true,
        actionItems: state.actionItems.map((item) => {
          if (item.uuid !== action.uuid) return item;
          return {
            ...item,
            checked: action.checked,
          };
        }),
      };
    }

    case "removeActionItem": {
      return {
        ...state,
        touched: true,
        actionItems: state.actionItems.reduce((acc, item) => {
          if (item.uuid !== action.uuid) return [...acc, item];

          // Deleting a not-yet-saved item, simply filter the item out
          if (item.action === "create") return acc;

          // Deleting an existing item, set action="delete"
          return [...acc, { ...item, action: "delete" }];
        }, [] as EditableActionItem[]),
      };
    }

    case "addActionItem": {
      return {
        ...state,
        touched: true,
        actionItems: state.actionItems
          .map((item) => ({
            ...item,
            autoFocus: false,
          }))
          .concat({
            uuid: uuidv4(),
            value: "",
            autoFocus: true,
            checked: false,
            action: "create",
          }),
      };
    }

    case "updateSummarySection": {
      return {
        ...state,
        touched: true,
        summaryByTopics: state.summaryByTopics.map((topic) => {
          if (topic.uuid !== action.uuid) return topic;
          return {
            ...topic,
            value: action.nextValue,
          };
        }),
      };
    }

    case "removeSummarySection": {
      return {
        ...state,
        touched: true,
        summaryByTopics: state.summaryByTopics.filter(
          (topic) => topic.uuid !== action.uuid
        ),
      };
    }

    case "addSummarySection": {
      return {
        ...state,
        touched: true,
        summaryByTopics: state.summaryByTopics
          .map((item) => ({
            ...item,
            autoFocus: false,
          }))
          .concat({
            uuid: uuidv4(),
            value: "",
            autoFocus: true,
            nodes: [],
          }),
      };
    }

    case "updateSummaryNodeInParentSection": {
      return {
        ...state,
        touched: true,
        summaryByTopics: state.summaryByTopics.map((topic) => {
          if (topic.uuid !== action.parentUuid) return topic;
          return {
            ...topic,
            nodes: topic.nodes.map((node) => {
              if (node.uuid !== action.uuid) return node;
              return {
                ...node,
                value: action.nextValue,
              };
            }),
          };
        }),
      };
    }

    case "removeSummaryNodeFromParentSection": {
      return {
        ...state,
        touched: true,
        summaryByTopics: state.summaryByTopics.map((topic) => {
          if (topic.uuid !== action.parentUuid) return topic;
          return {
            ...topic,
            nodes: topic.nodes.filter((node) => node.uuid !== action.uuid),
          };
        }),
      };
    }

    case "addSummaryNodeToParentSection": {
      return {
        ...state,
        touched: true,
        summaryByTopics: state.summaryByTopics.map((topic) => {
          if (topic.uuid !== action.parentUuid) return topic;
          return {
            ...topic,
            nodes: topic.nodes
              .map((item) => ({
                ...item,
                autoFocus: false,
              }))
              .concat({
                uuid: uuidv4(),
                value: "",
                autoFocus: true,
              }),
          };
        }),
      };
    }

    case "updateClient": {
      return {
        ...state,
        touched: true,
        client: action.client,
      };
    }
    case "updateMeetingName": {
      return {
        ...state,
        touched: true,
        meetingName: action.nextMeetingName,
      };
    }

    case "updateAttendees": {
      return {
        ...state,
        touched: true,
        attendees: action.nextAttendees,
      };
    }

    case "replaceState": {
      return {
        ...state,
        ...action.newState,
        touched: false,
      };
    }

    default: {
      return state;
    }
  }
};

export const mapNoteToReducerState = (
  note: NoteResponse | SerializeFrom<NoteResponse>,
  attendees: AttendeeOptions
): EditableNoteState => ({
  touched: false,
  meetingName: note.meetingName,
  client:
    note?.client?.uuid && note?.client?.name
      ? {
          uuid: note?.client?.uuid,
          name: note?.client?.name,
          type: ClientType.Unknown,
        }
      : undefined,
  actionItems: (note.actionItems ?? []).map((item) => ({
    uuid: item.uuid,
    value: item.content,
    checked: item.status === "complete",
    action: "keep",
    autoFocus: false,
  })),
  advisorNotes: (note.advisorNotes ?? []).map((value) => ({
    uuid: uuidv4(),
    value,
    autoFocus: false,
  })),
  keyTakeaways: (note.keyTakeaways ?? []).map((value) => ({
    uuid: uuidv4(),
    value,
    autoFocus: false,
  })),
  summaryByTopics: (note.summaryByTopics?.sections ?? []).map((topic) => ({
    autoFocus: false,
    uuid: uuidv4(),
    value: topic.topic,
    nodes: topic.bullets.map((node) => ({
      autoFocus: false,
      uuid: uuidv4(),
      value: node,
    })),
  })),
  attendees: attendees,
});

const trimNotEmpty = (item: { value: string }) => item.value.trim() !== "";

export const mapReducerStateToEditNoteArguments = (
  state: EditableNoteState
): EditNoteRequest => ({
  meetingName: state.meetingName,
  client: state.client
    ? {
        ...state.client,
        email:
          "email" in state.client && typeof state.client.email == "string"
            ? state.client.email
            : null,
      }
    : undefined,
  actionItems: state.actionItems.map(({ value, uuid, checked, action }) => {
    const trimmedValue = value.trim();
    return {
      action: trimmedValue === "" ? "delete" : action,
      item: {
        content: value,
        uuid,
        status: checked ? "complete" : "incomplete",
      },
    };
  }),
  advisorNotes: state.advisorNotes
    .filter(trimNotEmpty)
    .map(({ value }) => value),
  keyTakeaways: state.keyTakeaways
    .filter(trimNotEmpty)
    .map(({ value }) => value),
  summary: {
    sections: state.summaryByTopics
      .filter(trimNotEmpty)
      .map(({ value, nodes }) => ({
        topic: value,
        bullets: nodes.filter(trimNotEmpty).map(({ value }) => value),
      })),
  },
  attendees: state.attendees,
});
