import { useEffect, useState } from "react";
import { useFetcher } from "react-router";
import { toast } from "react-toastify";

import { AnswerStatus } from "~/@ui/AskAnythingModal";
import { SummarySection } from "~/api/openapi/generated";

type UseAskAnythingProps = {
  noteUuid: string;
  onAddSectionToSummary: (sectionIndex: number | null) => void;
};

function useAskAnything({
  noteUuid,
  onAddSectionToSummary,
}: UseAskAnythingProps) {
  const askFetcher = useFetcher();
  const [isAskModalOpen, setIsAskModalOpen] = useState<boolean>(false);
  const [query, setQuery] = useState<string>("");
  const [answerStatus, setAnswerStatus] = useState<AnswerStatus>(
    AnswerStatus.IDLE
  );
  const [answer, setAnswer] = useState<SummarySection | null>(null);
  const [searchQueryId, setSearchQueryId] = useState<string>("");
  const [sectionIndex, setSectionIndex] = useState<number | null>(null);

  // this function is mostly used when modal needs to be closed, along with resetting all local state; but it also supports opening the modal
  const changeModalState = (state: boolean) => {
    if (!state) {
      // reset local state
      setAnswer(null);
      setAnswerStatus(AnswerStatus.IDLE);
      setQuery("");
      setSectionIndex(null);
    }

    setIsAskModalOpen(state);
  };

  const startSearch = (searchString: string, sectionIndex: number | null) => {
    setSectionIndex(sectionIndex);
    setAnswer(null);
    setAnswerStatus(AnswerStatus.IN_PROCESS);
    setQuery(searchString);
    changeModalState(true);

    askMeAnythingApiCall(searchString);
  };

  const askMeAnythingApiCall = async (query: string) => {
    const formData = new FormData();
    formData.append("actionType", "search-note");
    formData.append("query", query);

    setAnswerStatus(AnswerStatus.IN_PROCESS);

    askFetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteUuid}`,
      encType: "multipart/form-data",
    });
  };

  const addOrReplaceSummaryTopic = (shouldReplace: boolean) => {
    const formData = new FormData();
    formData.append("actionType", "add-section-to-summary");
    formData.append("searchQueryId", searchQueryId);
    shouldReplace &&
      sectionIndex !== null &&
      formData.append("sectionToReplace", sectionIndex.toString());

    askFetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteUuid}/`,
      encType: "multipart/form-data",
    });
  };

  // handle API response
  useEffect(() => {
    if (askFetcher.state !== "idle" || !askFetcher.data) {
      return;
    }

    // if modal is no longer open, ignore
    if (!isAskModalOpen) {
      return;
    }

    if (askFetcher.data.actionType === "search-note") {
      if (askFetcher.data.success && askFetcher.data?.answer) {
        setAnswerStatus(AnswerStatus.SUCCESS);
        setAnswer(askFetcher.data.answer);
        setSearchQueryId(askFetcher.data.searchQueryId ?? "");
      } else {
        setAnswerStatus(AnswerStatus.FAILED);
      }
    } else if (askFetcher.data.actionType === "add-section-to-summary") {
      if (askFetcher.data.success) {
        toast.success("Your answer was added to the summary");
        setQuery("");
        onAddSectionToSummary(askFetcher.data.sectionIndex ?? null);
        changeModalState(false);
      } else {
        toast.error("Failed to add to summary");
      }
    }
  }, [askFetcher.data, askFetcher.state]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    isModalOpen: isAskModalOpen,
    setIsModalOpen: changeModalState,
    query,
    setQuery,
    answerStatus,
    setAnswerStatus,
    answer,
    startSearch,
    addOrReplaceSummaryTopic,
    askAnythingSubmit: () => askMeAnythingApiCall(query),
    sectionIndex,
  };
}

export default useAskAnything;
