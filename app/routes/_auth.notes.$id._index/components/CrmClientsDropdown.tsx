import { useCallback, useState } from "react";

import { VirtualizedCombobox } from "~/@ui/VirtualizedCombobox";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";
import LabeledValue from "~/types/LabeledValue";

type CrmClientsDropdownType = {
  placeholder?: string;
  leftIcon?: React.ReactNode;
  onChange: (value: ApiRoutersCrmClientResponse | undefined) => void;
  selectedClient?: ApiRoutersCrmClientResponse;
  searchOnLabel?: boolean;
  modal?: boolean;
  disabled?: boolean;
  itemSize?: number;
  maxHeightPx?: number;
  triggerClassName?: string;
};

const CrmClientsDropdown = (props: CrmClientsDropdownType) => {
  const {
    placeholder,
    leftIcon,
    onChange,
    selectedClient,
    searchOnLabel,
    modal,
    disabled,
    itemSize = 40,
    maxHeightPx = 200,
    triggerClassName,
  } = props;

  const [clients, setClients] = useState<ApiRoutersCrmClientResponse[]>([]);
  const [lastSearchTerm, setLastSearchTerm] = useState("");
  const [cursor, setCursor] = useState("");
  const [isFirstApiCall, setIsFirstApiCall] = useState(true); // true if this is the first time the API is called

  const loadClientsForCombobox = useCallback(
    async (searchTerm: string) => {
      let updatedSearchTerm = lastSearchTerm;
      let updatedCursor = cursor;
      let hasSearchTermChanged = false;

      if (lastSearchTerm !== searchTerm) {
        hasSearchTermChanged = true;
        updatedSearchTerm = searchTerm;
        updatedCursor = "";
      }

      setLastSearchTerm(updatedSearchTerm);
      setCursor(updatedCursor);

      if (!hasSearchTermChanged && !updatedCursor && !isFirstApiCall) {
        return;
      }

      setIsFirstApiCall(false);

      const { clients: options, nextPageToken } = await fetch(
        `/feapi/clients/get?searchTerm=${updatedSearchTerm}&cursor=${updatedCursor}&pageSize=20`
      ).then((res) => res.json());

      const optionsWithCrmId = options.filter(
        ({ crmId }: ApiRoutersCrmClientResponse) => !!crmId
      );

      setCursor(nextPageToken);
      let newClientList = hasSearchTermChanged
        ? optionsWithCrmId
        : [...clients, ...optionsWithCrmId];
      if (
        selectedClient &&
        !newClientList.some(
          (c: ApiRoutersCrmClientResponse) => c.uuid === selectedClient.uuid
        )
      ) {
        newClientList = [selectedClient, ...newClientList];
      }
      setClients(newClientList);
    },
    [lastSearchTerm, cursor, isFirstApiCall, selectedClient, clients]
  );

  const labelForClient = (
    client: ApiRoutersCrmClientResponse
  ): LabeledValue => {
    return {
      label: client.type ? `${client.name} (${client.type})` : client.name,
      value: client.uuid,
    };
  };

  const options = clients.map(labelForClient);

  return (
    <VirtualizedCombobox
      options={options}
      placeholder={placeholder}
      leftIcon={leftIcon}
      onChange={(item) => {
        const selectedClient = clients.find((c) => c.uuid === item?.value);
        onChange(selectedClient);
      }}
      disabled={disabled}
      selectedObject={
        selectedClient ? labelForClient(selectedClient) : undefined
      }
      searchOnLabel={searchOnLabel}
      loadOptions={loadClientsForCombobox}
      modal={modal}
      itemSize={itemSize}
      maxHeightPx={maxHeightPx}
      triggerClassName={triggerClassName}
    />
  );
};

export default CrmClientsDropdown;
