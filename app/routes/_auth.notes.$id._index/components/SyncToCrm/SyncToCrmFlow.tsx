import { useMemo, useState } from "react";
import { useRevalidator } from "react-router";
import { RefreshCcw } from "lucide-react";
import { toast } from "react-toastify";

import { Button } from "~/@shadcn/ui/button";
import {
  ApiRoutersCrmClientResponse,
  CRMSyncItemSelection,
  CRMUploadTarget,
  NoteResponse,
} from "~/api/openapi/generated";
import SyncDataSelectionModal from "./SyncDataSelectionModal";

import { getCardData } from "./utils";
import ClientSelectionModal from "./ClientSelectionModal";
import { EditableNoteActions } from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import TargetSelectionModal from "./TargetSelectionModal";
import { fetchPost } from "~/utils/fetch";
import { useFlag } from "~/context/flags";

enum Modal {
  Selection = "selection",
  ClientSelection = "client-selection",
  TargetSelection = "target-selection",
}

type SyncToCrmFlowProps = {
  currentClient?: ApiRoutersCrmClientResponse;
  dispatch?: React.Dispatch<EditableNoteActions>;
  data?: NoteResponse;
  noteId?: string;
  disabled?: boolean;
};
const SyncToCrmFlow = ({
  currentClient,
  dispatch,
  data,
  noteId,
  disabled,
}: SyncToCrmFlowProps) => {
  const [fetchedNoteData, setFetchedNoteData] = useState<
    NoteResponse | undefined
  >(undefined);
  const [selectedModal, setSelectedModal] = useState<string | null>();
  const [selections, setSelections] = useState<
    Record<string, CRMSyncItemSelection>
  >({});
  const [syncTargets, setSyncTargets] = useState<CRMUploadTarget[]>([]);
  const [displayTitle, setDisplayTitle] = useState<string | undefined>();
  const [displayDescription, setDisplayDescription] = useState<
    string | undefined
  >();

  const [isSaving, setIsSaving] = useState(false);

  const enableNoteTabsNameChanges = useFlag("EnableNoteDetailsPageNameChanges");

  const revalidator = useRevalidator();

  async function loadNoteData() {
    const noteData = await fetch(`/feapi/notes/id?noteId=${noteId}`).then(
      (res) => res.json()
    );

    setFetchedNoteData(noteData);
  }

  // when the S2C CTA is clicked
  const onClickCta = () => {
    // load the note data if it hasn't been provided or previously fetched
    if (!data && !fetchedNoteData && noteId) {
      // make API call using noteId
      loadNoteData();
    }

    // open client selection dropdown if client has not been selected; else open the selection modal
    if (!currentClient) {
      setSelectedModal(Modal.ClientSelection);
    } else {
      setSelectedModal(Modal.Selection);
    }
  };

  const noteData = data || fetchedNoteData;

  const closeAll = () => {
    setSelectedModal(null);
  };

  const saveClient = async (selectedClient: ApiRoutersCrmClientResponse) => {
    setIsSaving(true);

    try {
      if (!dispatch) {
        return;
      }

      dispatch({
        type: "updateClient",
        client: selectedClient ?? null,
      });

      const { success } = await fetchPost("/feapi/notes/save", {
        noteId: noteData?.uuid,
        clientName: selectedClient.name,
        clientId: selectedClient.uuid,
      });

      if (success) {
        setSelectedModal(Modal.Selection); // trigger the next phase
      } else {
        throw new Error("Failed to save client");
      }
    } catch (e) {
      toast.error("Failed to save client. Please try again!!!", {
        autoClose: 2000,
        toastId: "save-client-error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const saveToCrmFromAttributeSelection = async (
    syncItems: Record<string, CRMSyncItemSelection>
  ) => {
    // save `syncItems` in case upload targets flow is invoked
    setSelections(syncItems);

    saveToCrm(syncItems, "");
  };

  const saveToCrmFromTargetSelection = (uploadTargetId: string) => {
    saveToCrm(selections, uploadTargetId);
  };

  const saveToCrm = async (
    selections: Record<string, CRMSyncItemSelection>,
    uploadTargetId: string
  ) => {
    setIsSaving(true);

    try {
      const {
        success,
        displayTitle,
        displayDescription,
        userInputRequired,
        uploadTargetOptions,
        error,
      } = await fetchPost("/feapi/notes/sync-to-crm", {
        noteId: noteData?.uuid,
        uploadTargetId,
        syncItems: selections,
      });

      // handle outright failures
      if (!success) {
        toast.error(`Failed to sync note to CRM. ${error}`, {
          autoClose: 2000,
          toastId: "sync-to-crm-error",
        });
        return;
      }

      // happy flow
      if (!userInputRequired) {
        closeAll();

        toast.success("Note synced to CRM", {
          autoClose: 2000,
          toastId: "sync-to-crm-success",
        });
        // Ensure toast is displayed before revalidating
        setTimeout(() => {
          revalidator.revalidate();
        }, 0);

        return;
      }

      // in case user input is required, open the target selection modal
      if (userInputRequired && uploadTargetOptions) {
        setSelectedModal(Modal.TargetSelection);
        setSyncTargets(uploadTargetOptions);
        setDisplayTitle(displayTitle);
        setDisplayDescription(displayDescription);
        return;
      }

      // There won't be any case where `userInputRequired` is true and `uploadTargetOptions` is undefined. Right? RIGHT??
      throw new Error("Unexpected response from server");
    } catch (e) {
      toast.error("Failed to sync note to CRM. Please try again", {
        autoClose: 2000,
        toastId: "sync-to-crm-error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const selectionModalData = useMemo(
    () => getCardData(noteData, enableNoteTabsNameChanges),
    [noteData, enableNoteTabsNameChanges]
  );

  return (
    <>
      <Button
        onClick={onClickCta}
        variant="ghost"
        className=""
        data-onboarding="sync-crm-cta"
        aria-label="Sync to CRM"
        disabled={disabled}
      >
        <RefreshCcw />

        <span className="hidden sm:block">Sync to CRM</span>
      </Button>

      {selectedModal === Modal.ClientSelection && (
        <ClientSelectionModal
          onClose={closeAll}
          onContinue={saveClient}
          currentClient={currentClient}
          isSaving={isSaving}
        />
      )}

      {selectedModal === Modal.Selection && (
        <SyncDataSelectionModal
          dataSet={selectionModalData}
          onClose={closeAll}
          onContinue={saveToCrmFromAttributeSelection}
          isSaving={isSaving}
        />
      )}

      {selectedModal === Modal.TargetSelection && (
        <TargetSelectionModal
          onClose={closeAll}
          onContinue={saveToCrmFromTargetSelection}
          targets={syncTargets}
          title={displayTitle || "Select CRM Upload Target"}
          description={displayDescription || "Upload target"}
          isSaving={isSaving}
        />
      )}
    </>
  );
};

export default SyncToCrmFlow;
