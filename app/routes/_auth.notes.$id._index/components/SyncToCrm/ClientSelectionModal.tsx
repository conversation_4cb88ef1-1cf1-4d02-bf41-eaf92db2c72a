import { useState } from "react";
import { BriefcaseBusiness } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Button } from "~/@shadcn/ui/button";

import CrmClientsDropdown from "../CrmClientsDropdown";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";

export type ClientSelectionModalProps = {
  onClose: () => void;
  onContinue: (selectedClient: ApiRoutersCrmClientResponse) => void;
  currentClient: ApiRoutersCrmClientResponse | undefined;
  isSaving: boolean;
};

const ClientSelectionModal = ({
  onClose,
  onContinue,
  currentClient,
  isSaving,
}: ClientSelectionModalProps) => {
  const [selectedClient, setSelectedClient] = useState<
    ApiRoutersCrmClientResponse | undefined
  >(currentClient); // default to current client; will be updated by user

  const onChangeDropdown = (item: ApiRoutersCrmClientResponse | undefined) => {
    setSelectedClient(item);
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Which client do you want to sync to?
          </DialogTitle>
        </DialogHeader>
        <FormField id="clientId" name="clientId">
          <FormLabel className="text-sm font-medium text-gray-700">
            CRM Client
          </FormLabel>
          <CrmClientsDropdown
            placeholder="Select a client"
            leftIcon={<BriefcaseBusiness />}
            onChange={onChangeDropdown}
            selectedClient={selectedClient}
            searchOnLabel={true}
            modal
          />
        </FormField>
        <DialogFooter className="pt-6">
          <Button onClick={onClose} variant="ghost">
            Cancel
          </Button>
          <Button
            disabled={!selectedClient || isSaving}
            onClick={() => selectedClient && onContinue(selectedClient)}
          >
            {isSaving ? "Saving..." : "Confirm"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClientSelectionModal;
