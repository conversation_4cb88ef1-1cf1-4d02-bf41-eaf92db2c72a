import { useState } from "react";
import { BriefcaseBusiness } from "lucide-react";

import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogFooter,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Button } from "~/@shadcn/ui/button";

import { CRMUploadTarget } from "~/api/openapi/generated";
import { Combobox } from "~/@ui/Combobox";

type TargetSelectionModalProps = {
  onClose: () => void;
  onContinue: (uploadTargetId: string) => void;
  title: string;
  description: string;
  targets: CRMUploadTarget[];
  isSaving: boolean;
};

const TargetSelectionModal = ({
  onClose,
  onContinue,
  title,
  description,
  targets,
  isSaving,
}: TargetSelectionModalProps) => {
  const [selectedTarget, setSelectedTarget] = useState<
    CRMUploadTarget | undefined
  >();

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:w-fit sm:min-w-[512px] sm:max-w-[75%]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
        </DialogHeader>
        <FormField
          id="uploadTarget"
          name="uploadTarget"
          className="overflow-hidden"
        >
          <FormLabel className="text-sm font-medium text-gray-700">
            {description}
          </FormLabel>
          <Combobox
            options={targets.map((target) => ({
              value: target.id,
              label: target.name,
            }))}
            placeholder="Select an upload target"
            leftIcon={<BriefcaseBusiness className="shrink-0" />}
            onChange={(value) =>
              setSelectedTarget(targets.find((t) => t.id === value))
            }
            selected={selectedTarget?.id}
            searchOnLabel={true}
            modal
          />
        </FormField>
        <DialogFooter className="pt-6">
          <Button onClick={onClose} variant="ghost">
            Cancel
          </Button>
          <Button
            onClick={() => {
              selectedTarget && onContinue(selectedTarget.id);
            }}
            disabled={!selectedTarget || isSaving}
          >
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TargetSelectionModal;
