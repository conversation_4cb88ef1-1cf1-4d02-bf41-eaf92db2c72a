import { useNavigate } from "react-router";
import { Steps } from "intro.js-react";

import useOnboarding from "~/utils/useOnboarding";
import postMeetingTutorialSteps from "../utils/postMeetingTutorialSteps";
import summaryTabTutorialSteps from "../utils/summaryTabTutorialSteps";
import syncToCrmTutorialSteps from "../utils/syncToCrmTutorialSteps";
import emailTutorialSteps from "../utils/emailTutorialSteps";

const NotesOnboarding = ({
  enableNoteSearch,
  currentTab,
}: {
  enableNoteSearch: boolean;
  currentTab: string;
}) => {
  const navigate = useNavigate();

  const {
    isTutorialEnabled: isPostMeetingTutorialEnabled,
    completeTutorial: completePostMeetingTutorial,
  } = useOnboarding("post-meeting", {
    triggerViaUrl: true,
    sectionName: "overview",
  });
  const {
    isTutorialEnabled: isSummaryTabTutorialEnabled,
    completeTutorial: completeSummaryTabTutorial,
  } = useOnboarding("post-meeting-summary-tab", {
    triggerViaUrl: true,
    sectionName: "overview",
    checks: [() => currentTab === "summary"],
  });
  const {
    isTutorialEnabled: isSyncToCrmTutorialEnabled,
    completeTutorial: completeSyncToCrmTutorial,
  } = useOnboarding("post-meeting", {
    triggerViaUrl: true,
    sectionName: "sync-to-crm",
  });
  const {
    isTutorialEnabled: isEmailTutorialEnabled,
    completeTutorial: completeEmailTutorial,
  } = useOnboarding("post-meeting", {
    triggerViaUrl: true,
    sectionName: "follow-up-email",
  });

  const onExitPostMeetingTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitPostMeetingTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      completePostMeetingTutorial();
    }
  };

  const onCompleteSummaryTabTutorial = () => {
    completeSummaryTabTutorial();
    navigate("/notes", { replace: true });
  };
  const onExitSummaryTabTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitSummaryTabTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteSummaryTabTutorial();
    }
  };

  const onCompleteSyncToCrmTutorial = () => {
    completeSyncToCrmTutorial();
    navigate("/notes", { replace: true });
  };
  const onExitSyncToCrmTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitSyncToCrmTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteSyncToCrmTutorial();
    }
  };

  const onCompleteEmailTutorial = () => {
    completeEmailTutorial();
    navigate("/notes", { replace: true });
  };
  const onExitEmailTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitEmailTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteEmailTutorial();
    }
  };

  return (
    <>
      <Steps
        enabled={isPostMeetingTutorialEnabled && !isSummaryTabTutorialEnabled}
        steps={postMeetingTutorialSteps({ enableNoteSearch })}
        initialStep={0}
        onExit={onExitPostMeetingTutorial}
        onComplete={completePostMeetingTutorial}
        options={{
          exitOnOverlayClick: false,
        }}
      />

      <Steps
        enabled={isSummaryTabTutorialEnabled}
        steps={summaryTabTutorialSteps}
        initialStep={0}
        onExit={onExitSummaryTabTutorial}
        onComplete={onCompleteSummaryTabTutorial}
        options={{
          exitOnOverlayClick: false,
        }}
      />

      <Steps
        enabled={isSyncToCrmTutorialEnabled}
        steps={syncToCrmTutorialSteps}
        initialStep={0}
        onExit={onExitSyncToCrmTutorial}
        onComplete={onCompleteSyncToCrmTutorial}
        options={{
          exitOnOverlayClick: false,
        }}
      />

      <Steps
        enabled={isEmailTutorialEnabled}
        steps={emailTutorialSteps}
        initialStep={0}
        onExit={onExitEmailTutorial}
        onComplete={onCompleteEmailTutorial}
        options={{
          exitOnOverlayClick: false,
        }}
      />
    </>
  );
};

export default NotesOnboarding;
