import { HelpCircle } from "lucide-react";

import { Typography } from "~/@ui/Typography";
import {
  INTERCOM_LAUNCHER_SELECTOR,
  useIntercom,
} from "~/third-party/Intercom/Intercom";

const ContactSupportTab = ({
  header,
  body,
}: {
  header: string;
  body: string;
}) => {
  const { isIntercomAvailable } = useIntercom();
  return (
    <div className="flex flex-col items-center justify-center gap-3 rounded-lg bg-yellow-50 p-6 text-gray-700 shadow-md">
      <Typography variant="h6" className="font-semibold">
        {header}
      </Typography>
      <Typography variant="body2" className="text-center">
        {body}
      </Typography>
      {isIntercomAvailable && (
        <div className="mt-4 flex items-center gap-2">
          <HelpCircle className="h-6 w-6 text-yellow-700" />
          <button
            id={INTERCOM_LAUNCHER_SELECTOR}
            className="text-sm font-medium text-yellow-700 underline hover:text-yellow-800"
          >
            Contact Support
          </button>
        </div>
      )}
    </div>
  );
};

export default ContactSupportTab;
