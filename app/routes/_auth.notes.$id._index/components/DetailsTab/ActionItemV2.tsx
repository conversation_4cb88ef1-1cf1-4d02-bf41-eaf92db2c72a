// TODO: @<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Remove the "V2" aspect once we are happy with the new implementation
import { useEffect, useState } from "react";
import {
  ArrowUpRight,
  Calendar,
  Check,
  Circle,
  CircleCheckBig,
  Edit,
  Eye,
  User,
  X,
} from "lucide-react";

import { But<PERSON> } from "~/@shadcn/ui/button";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { Label } from "~/@shadcn/ui/label";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { cn } from "~/@shadcn/utils";
import { Spinner } from "~/@ui/assets/Spinner";
import { useUserAgent } from "~/context/userAgent";
import { fetchPost } from "~/utils/fetch";
import { useRevalidator } from "react-router";
import { Combobox } from "~/@ui/Combobox";
import { DatePicker } from "~/@ui/DatePicker";
import { toast } from "react-toastify";
import { TaskResponse } from "~/api/openapi/generated";

type Props = {
  parentNoteUuid: string;
  data: TaskResponse;
  disabled: boolean;
};

const ActionItemV2 = ({ parentNoteUuid, data, disabled }: Props) => {
  const {
    uuid,
    title: titleProp,
    dueDate: dueDateProp,
    completed,
    assignee: assigneeProp,
    // assignees,
  } = data;
  const [isProcessing, setIsProcessing] = useState(false);

  const [title, setTitle] = useState(titleProp);
  const [checked, setChecked] = useState(completed);
  const [assignee, setAssignee] = useState<Assignee | null>(assigneeProp);
  const [assignees, setPossibleAssignees] = useState<Assignee[]>([]);
  const [dueDate, setDueDate] = useState<Date | undefined>(
    dueDateProp ? new Date(dueDateProp) : undefined
  );

  const [isEditingTitle, setIsEditingTitle] = useState(false);

  const { isMobile } = useUserAgent();
  const revalidator = useRevalidator();

  // TODO: @debojyotighosh ENG-1895 Remove this work-around once we get the new API to "get all users"
  // get `assignees`
  useEffect(() => {
    // check for uuid
    if (!uuid) {
      return;
    }

    (async () => {
      try {
        const task = await fetch(`/feapi/tasks/get?id=${uuid}`).then((res) =>
          res.json()
        );

        if (!task) {
          throw new Error("No task found");
        }

        const { assignees } = task;
        setPossibleAssignees(assignees);
        setIsProcessing(false);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error("Failed to fetch task", e);
      }
    })();
  }, [uuid]);

  const toggleChecked = async () => {
    const value = !checked;
    setChecked(value);

    const isSaved = await saveChanges({
      key: "completed",
      value,
    });

    if (!isSaved) {
      setChecked(completed); // reset
      toast.error("Could not update task");
    }
  };

  const updateAssignee = async (assignee: { uuid: string; name: string }) => {
    const isSaved = await saveChanges({
      key: "assignee",
      value: assignee.uuid,
    });

    if (isSaved) {
      setAssignee(assignee);
    } else {
      toast.error("Could not update assignee");
    }
  };

  const updateDate = async (nextDate?: Date) => {
    if (!nextDate) {
      return;
    }

    const isSaved = await saveChanges({
      key: "dueDate",
      value: nextDate,
    });

    if (isSaved) {
      setDueDate(nextDate);
    } else {
      toast.error("Could not update due date");
    }
  };

  const updateTitle = async () => {
    if (!title) {
      // TODO: @debojyotighosh ENG-1895 Offer to delete
      return;
    }

    const isSaved = await saveChanges({
      key: "title",
      value: title,
    });

    if (!isSaved) {
      setTitle(titleProp);
      toast.error("Could not update title");
    }
  };

  const saveChanges = async ({ key, value }: { key: string; value: any }) => {
    return new Promise((resolve, reject) => {
      let couldSave = false;
      setIsProcessing(true);

      fetchPost("/feapi/tasks/edit", {
        uuid,
        parentNoteUuid,
        [key]: value,
      })
        .then(({ success }) => {
          if (success) {
            couldSave = true;

            // revalidate
            revalidator.revalidate();
          }
        })
        .finally(() => {
          setIsProcessing(false);
          resolve(couldSave);
        });
    });
  };

  return (
    <div
      className={cn(
        "group flex items-start gap-2 rounded-md border border-slate-200 p-2 hover:bg-muted",
        completed && "opacity-80",
        isProcessing && "pointer-events-none opacity-35"
      )}
    >
      {/* toggle */}
      <div
        className={cn(
          "flex size-6 shrink-0 cursor-pointer items-center justify-center",
          disabled && "opacity-25"
        )}
        onClick={toggleChecked}
      >
        {isProcessing && <Spinner />}
        {!isProcessing &&
          (checked ? (
            <CircleCheckBig size={20} className="text-success" />
          ) : (
            <Circle size={20} />
          ))}
      </div>

      <div className="grow">
        {/* title */}
        {isEditingTitle ? (
          <TextareaGrowable
            value={title}
            placeholder="Add an action item"
            className="flex-grow border-none text-sm !opacity-100 focus:outline-none"
            onChange={(event) => {
              setTitle(event.currentTarget.value);
            }}
            onBlur={() => {
              setIsEditingTitle(false);
              updateTitle();
            }}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                event.preventDefault();

                setIsEditingTitle(false);
                updateTitle();
              }
            }}
            autoFocus
          />
        ) : (
          <div
            className={cn("flex w-full text-sm", completed && "line-through")}
          >
            <span>
              <span className="mr-2">{title}</span>

              {/* edit icon */}
              <Edit
                size={20}
                className={cn(
                  "-mt-0.5 inline shrink-0 cursor-pointer align-middle text-muted-foreground",
                  !isMobile &&
                    "opacity-25 transition-opacity duration-200 group-hover:opacity-100"
                )}
                onClick={() => setIsEditingTitle(true)}
              />
            </span>
          </div>
        )}

        <div className="mt-2 flex shrink-0 flex-col gap-1 sm:flex-row sm:gap-4 md:mt-1 md:items-center">
          {/* assignee */}
          {assignees && (
            <div className="flex items-center gap-1">
              <User size={16} className="shrink-0 text-muted-foreground" />
              {!isMobile && (
                <span className="text-sm text-muted-foreground">Assignee</span>
              )}

              <Combobox
                placeholder={"Assignee"}
                options={assignees.map(({ name, uuid }) => ({
                  label: name,
                  value: uuid,
                }))}
                selected={assignee?.uuid}
                onChange={(nextValue) => {
                  const selectedAssignee = (assignees || []).find(
                    (ele) => ele.uuid === nextValue
                  );
                  if (selectedAssignee) {
                    updateAssignee(selectedAssignee);
                  }
                }}
                triggerClassName="py-1 px-2 text-xs min-w-32 w-fit ml-1"
              />
            </div>
          )}

          {!isMobile && (
            <span className="h-4 w-[1px] bg-muted-foreground opacity-50" />
          )}

          <div className="flex grow items-center gap-1">
            {/* due date */}
            <Calendar size={16} className="shrink-0 text-muted-foreground" />
            {!isMobile && (
              <span className="text-sm text-muted-foreground">Due Date</span>
            )}
            <DatePicker
              id={`task-due-date-${uuid}`}
              name="date"
              date={dueDate}
              onSelect={(nextDate) => updateDate(nextDate)}
              triggerClassName="py-1 px-2 text-xs min-h-fit ml-1"
              showIcon={false}
            />

            {/* view button for mobile */}
            {isMobile && (
              <ArrowUpRight
                size={22}
                className="ml-auto self-end text-muted-foreground sm:self-center"
                onClick={() => (window.location.href = `/tasks/${uuid}`)}
              />
            )}
          </div>
        </div>
      </div>

      {/* view icon for desktop */}
      {!isMobile && (
        <ArrowUpRight
          size={22}
          className={cn(
            "ml-auto shrink-0 cursor-pointer text-muted-foreground",
            !isMobile &&
              "opacity-25 transition-opacity duration-200 group-hover:opacity-100"
          )}
          onClick={() => (window.location.href = `/tasks/${uuid}`)}
        />
      )}
    </div>
  );
};

function getWorkflowStyles() {
  return {
    onboarding: "bg-muted-foreground text-muted",
  };
}

type Assignee = {
  uuid: string;
  name: string;
};

export default ActionItemV2;
