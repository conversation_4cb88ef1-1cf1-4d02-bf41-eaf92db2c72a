import { Crown, Users, Calendar, Activity, Info, Gauge } from "lucide-react";
import {
  PlanF<PERSON>ure,
  PlanUser,
  SectionItemPlanDetails,
} from "~/api/openapi/generated";
import { Badge } from "~/@shadcn/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "~/@shadcn/ui/card";
import { Progress } from "~/@shadcn/ui/progress";
import { Typography } from "~/@ui/Typography";
import { AttendeeTag } from "~/@ui/attendees/attendeeTags";
import { v4 as uuidv4 } from "uuid";

type SettingTypePlanDetailsProps = SectionItemPlanDetails;

const PlanFeatureCard = ({
  feature,
  users,
}: {
  feature: PlanFeature;
  users: PlanUser[];
}) => {
  const usagePercent =
    feature.licenseCount > 0
      ? (feature.userUuids.length / feature.licenseCount) * 100
      : 0;

  const selectedUsers = feature.userUuids
    .map((userUuid) => {
      const user = users.find((u) => u.uuid === userUuid);
      if (!user)
        return {
          uuid: uuidv4(),
          name: "Unknown User",
          type: "user" as const,
        };
      return {
        uuid: user.uuid,
        name: user.email,
        type: "user" as const,
      };
    })
    .filter((user) => user !== undefined);

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Activity className="h-5 w-5 text-primary" />
          {feature.label}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Usage</span>
            <span className="font-medium">
              {feature.userUuids.length} / {feature.licenseCount}
            </span>
          </div>
          <Progress value={usagePercent} className="h-2" />
        </div>

        <div className="space-y-2">
          <Typography
            variant="body3"
            className="font-medium text-muted-foreground"
          >
            Licensed Users ({selectedUsers.length})
          </Typography>

          {selectedUsers.length > 0 ? (
            <div className="flex max-h-40 flex-wrap gap-2 overflow-auto rounded-md border border-input p-2">
              {selectedUsers.map((user) => (
                <AttendeeTag key={user.uuid} {...user} />
              ))}
            </div>
          ) : (
            <div className="flex min-h-[40px] items-center justify-center rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
              No users assigned to this feature
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const MeetingUsageGauge = ({
  used,
  total,
  period,
}: {
  used: number;
  total: number;
  period: string;
}) => {
  const usagePercent = total > 0 ? (used / total) * 100 : 0;
  const remaining = Math.max(0, total - used);

  let statusColor = "text-success";
  let progressColor = "bg-success";

  if (usagePercent > 80) {
    statusColor = "text-destructive";
    progressColor = "bg-destructive";
  } else if (usagePercent > 60) {
    statusColor = "text-warning";
    progressColor = "bg-warning";
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calendar className="h-5 w-5 text-primary" />
          Meeting Usage
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2 text-center">
          <div className={`text-3xl font-bold ${statusColor}`}>{used}</div>
          <div className="text-sm text-muted-foreground">
            {used == 1 ? "meeting used this" : "meetings used this"}{" "}
            {period === "monthly"
              ? "month"
              : period === "yearly"
                ? "year"
                : "plan term"}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">{Math.round(usagePercent)}%</span>
          </div>
          <div className="relative">
            <Progress value={usagePercent} className="h-3" />
            <div
              className={`absolute inset-0 h-3 rounded-full ${progressColor} transition-all`}
              style={{ width: `${Math.min(usagePercent, 100)}%` }}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 pt-2">
          <div className="text-center">
            <div className="text-xl font-semibold text-muted-foreground">
              {remaining}
            </div>
            <div className="text-xs text-muted-foreground">Remaining</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-semibold text-muted-foreground">
              {total}
            </div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const SettingTypePlanDetails = ({
  label,
  planName,
  enabledFeatures,
  organizationPlanDetails,
}: SettingTypePlanDetailsProps) => (
  <div className="mt-6 space-y-6">
    <div className="flex items-center gap-3">
      <Crown className="h-6 w-6 text-primary" />
      <Typography variant="h3">{planName || label}</Typography>
    </div>

    <div className="space-y-4">
      <Typography variant="h4">Enabled Features</Typography>
      <div className="flex flex-wrap gap-2">
        {enabledFeatures && enabledFeatures.length > 0 ? (
          <>
            {enabledFeatures.map((feature, index) => (
              <Badge key={index} variant="secondary" className="text-md">
                {feature}
              </Badge>
            ))}
          </>
        ) : (
          <Typography variant="body2" className="text-muted-foreground">
            No features enabled for this plan.
          </Typography>
        )}
      </div>
    </div>

    {organizationPlanDetails ? (
      <>
        <div className="mt-6 space-y-6">
          <div className="flex items-center gap-3">
            <Gauge className="h-6 w-6 text-primary" />
            <Typography variant="h4">Plan Usage</Typography>
          </div>
        </div>
        <div className="space-y-6">
          <MeetingUsageGauge
            used={organizationPlanDetails.meetingsUsedThisTerm}
            total={organizationPlanDetails.meetingsAllowedPerTerm}
            period={organizationPlanDetails.planTerm}
          />
          {organizationPlanDetails.features &&
            organizationPlanDetails.features.length > 0 && (
              <div className="space-y-4">
                <Typography variant="h4" className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Plan Features
                </Typography>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                  {organizationPlanDetails.features.map((feature) => (
                    <PlanFeatureCard
                      key={feature.id}
                      feature={feature}
                      users={organizationPlanDetails.users ?? []}
                    />
                  ))}
                </div>
              </div>
            )}{" "}
        </div>
      </>
    ) : (
      <Typography variant="body2" className="text-muted-foreground">
        No features available for this plan. Contact support for more
        information.
      </Typography>
    )}
  </div>
);

export default SettingTypePlanDetails;
