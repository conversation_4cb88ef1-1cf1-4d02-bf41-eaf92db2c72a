import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import SettingTypePlanDetails from "./SettingTypePlanDetails";
import {
  PlanFeature,
  PlanUser,
  SectionItemPlanDetails,
} from "~/api/openapi/generated";

const fakePlanFeature = (overrides?: Partial<PlanFeature>): PlanFeature => ({
  id: "feature-1",
  label: "Advanced Analytics",
  licenseCount: 5,
  userUuids: ["user-1", "user-2"],
  ...overrides,
});

const fakePlanUser = (overrides?: Partial<PlanUser>): PlanUser => ({
  uuid: "user-1",
  name: "<PERSON>",
  email: "<EMAIL>",
  ...overrides,
});

const fakeSectionItemPlanDetails = (
  overrides?: Partial<SectionItemPlanDetails>
): SectionItemPlanDetails => ({
  id: "plan-1",
  label: "Professional Plan",
  planName: "Pro Plan",
  enabledFeatures: ["Feature A", "Feature B"],
  organizationPlanDetails: {
    planTerm: "monthly" as const,
    meetingsAllowedPerTerm: 100,
    meetingsUsedThisTerm: 45,
    features: [fakePlanFeature()],
    users: [fakePlanUser()],
  },
  ...overrides,
});

describe("PlanFeatureCard", () => {
  it("renders feature information correctly", () => {
    const feature = fakePlanFeature({
      label: "Video Recording",
      licenseCount: 10,
      userUuids: ["user-1", "user-2", "user-3"],
    });
    const users = [
      fakePlanUser({ uuid: "user-1", email: "<EMAIL>" }),
      fakePlanUser({ uuid: "user-2", email: "<EMAIL>" }),
      fakePlanUser({ uuid: "user-3", email: "<EMAIL>" }),
    ];

    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 45,
        features: [feature],
        users,
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Video Recording")).toBeInTheDocument();
    expect(screen.getByText("3 / 10")).toBeInTheDocument();
    expect(screen.getByText("Licensed Users (3)")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("handles zero license count", () => {
    const feature = fakePlanFeature({
      licenseCount: 0,
      userUuids: [],
    });
    const users = [fakePlanUser()];

    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 45,
        features: [feature],
        users,
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("0 / 0")).toBeInTheDocument();
  });

  it("shows message when no users are assigned", () => {
    const feature = fakePlanFeature({
      userUuids: [],
    });
    const users = [fakePlanUser()];

    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 45,
        features: [feature],
        users,
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(
      screen.getByText("No users assigned to this feature")
    ).toBeInTheDocument();
  });

  it("handles missing users in user list", () => {
    const feature = fakePlanFeature({
      userUuids: ["user-1", "nonexistent-user"],
    });
    const users = [
      fakePlanUser({ uuid: "user-1", email: "<EMAIL>" }),
    ];

    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 45,
        features: [feature],
        users,
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Licensed Users (2)")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("Unknown User")).toBeInTheDocument();
  });
});

describe("MeetingUsageGauge", () => {
  it("displays meeting usage information correctly", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 45,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("meetings used this month")).toBeInTheDocument();
    expect(screen.getByText("45%")).toBeInTheDocument();
    expect(screen.getByText("Remaining")).toBeInTheDocument();
    expect(screen.getByText("Total")).toBeInTheDocument();
  });

  it("shows correct period text for yearly plan", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "yearly" as const,
        meetingsAllowedPerTerm: 1200,
        meetingsUsedThisTerm: 300,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("meetings used this year")).toBeInTheDocument();
  });

  it("shows correct period text for other plan terms", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "custom" as any,
        meetingsAllowedPerTerm: 500,
        meetingsUsedThisTerm: 100,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(
      screen.getByText("meetings used this plan term")
    ).toBeInTheDocument();
  });

  it("handles zero total meetings", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 0,
        meetingsUsedThisTerm: 0,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("meetings used this month")).toBeInTheDocument();
    expect(screen.getByText("0%")).toBeInTheDocument();
    expect(screen.getByText("Remaining")).toBeInTheDocument();
    expect(screen.getByText("Total")).toBeInTheDocument();
  });

  it("shows correct percentage calculation", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 200,
        meetingsUsedThisTerm: 150,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("75%")).toBeInTheDocument();
    expect(screen.getByText("Remaining")).toBeInTheDocument();
  });

  it("handles usage exceeding total", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 150,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("150%")).toBeInTheDocument();
    expect(screen.getByText("Remaining")).toBeInTheDocument();
  });
});

describe("SettingTypePlanDetails", () => {
  it("renders plan information correctly", () => {
    const props = fakeSectionItemPlanDetails();

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Pro Plan")).toBeInTheDocument();
    expect(screen.getByText("Enabled Features")).toBeInTheDocument();
    expect(screen.getByText("Feature A")).toBeInTheDocument();
    expect(screen.getByText("Feature B")).toBeInTheDocument();
  });

  it("uses label when planName is not provided", () => {
    const props = fakeSectionItemPlanDetails({
      planName: undefined,
      label: "Basic Plan",
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Basic Plan")).toBeInTheDocument();
  });

  it("shows message when no features are enabled", () => {
    const props = fakeSectionItemPlanDetails({
      enabledFeatures: [],
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(
      screen.getByText("No features enabled for this plan.")
    ).toBeInTheDocument();
  });

  it("shows message when enabledFeatures is undefined", () => {
    const props = fakeSectionItemPlanDetails({
      enabledFeatures: undefined,
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(
      screen.getByText("No features enabled for this plan.")
    ).toBeInTheDocument();
  });

  it("does not render admin section when organizationPlanDetails is not provided", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: undefined,
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.queryByText("Plan Usage")).not.toBeInTheDocument();
    expect(screen.queryByText("Meeting Usage")).not.toBeInTheDocument();
  });

  it("renders admin section with meeting usage", () => {
    const props = fakeSectionItemPlanDetails();

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Plan Usage")).toBeInTheDocument();
    expect(screen.getByText("Meeting Usage")).toBeInTheDocument();
  });

  it("renders plan features section when features exist", () => {
    const props = fakeSectionItemPlanDetails();

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Plan Features")).toBeInTheDocument();
    expect(screen.getByText("Advanced Analytics")).toBeInTheDocument();
  });

  it("does not render plan features section when no features exist", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: {
        planTerm: "monthly" as const,
        meetingsAllowedPerTerm: 100,
        meetingsUsedThisTerm: 45,
        features: [],
        users: [],
      },
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.queryByText("Plan Features")).not.toBeInTheDocument();
  });

  it("handles null planName", () => {
    const props = fakeSectionItemPlanDetails({
      planName: null,
      label: "Fallback Plan",
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.getByText("Fallback Plan")).toBeInTheDocument();
  });

  it("handles null organizationPlanDetails", () => {
    const props = fakeSectionItemPlanDetails({
      organizationPlanDetails: null,
    });

    render(<SettingTypePlanDetails {...props} />);

    expect(screen.queryByText("Plan Usage")).not.toBeInTheDocument();
  });
});
