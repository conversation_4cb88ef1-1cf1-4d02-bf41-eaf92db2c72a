import { useState } from "react";
import pkceChallenge from "pkce-challenge";

import { cn } from "~/@shadcn/utils";
import {
  SectionItemIntegrationCard,
  LabeledEntity,
  SectionItemIntegrationCards,
} from "~/api/openapi/generated";

import { GoogleCalendarIcon } from "~/@ui/assets/GoogleCalendarIcon";
import { GoogleMeetIcon } from "~/@ui/assets/GoogleMeetIcon";
import { MicrosoftTeamsIcon } from "~/@ui/assets/MicrosoftTeamsIcon";
import { OutlookIcon } from "~/@ui/assets/OutlookIcon";
import { RedtailLogo } from "~/@ui/assets/RedtailLogo";
import { SalesforceIcon } from "~/@ui/assets/SalesforceIcon";
import { WealthboxIcon } from "~/@ui/assets/WealthboxIcon";
import { WebexIcon } from "~/@ui/assets/WebexIcon";
import { ZoomIcon } from "~/@ui/assets/ZoomIcon";
import { Button } from "~/@shadcn/ui/button";
import advisorEngine from "~/@ui/assets/advisor-engine-logo.png";
import amplify from "~/@ui/assets/amplify-logo.png";
import bento from "~/@ui/assets/bento-logo.jpg";
import blackDiamond from "~/@ui/assets/black-diamond-logo.png";
import dynamics from "~/@ui/assets/dynamics-logo.webp";
import practifi from "~/@ui/assets/practifi-logo.png";
import salenticaElements from "~/@ui/assets/salentica-elements-logo.png";
import salenticaEngage from "~/@ui/assets/salentica-engage-logo.png";
import tamarac from "~/@ui/assets/tamarac-logo.png";
import xlr8 from "~/@ui/assets/xlr8-logo.png";
import hubspot from "~/@ui/assets/hubspot-logo.png";
import { toast } from "react-toastify";

const IntegrationCards = ({
  id,
  label,
  filters,
  cards,
}: SectionItemIntegrationCards) => {
  const [activeFilter, setActiveFilter] = useState("");

  const handleSelectFilter = (filterId: string) => {
    setActiveFilter(filterId);
  };

  const filteredCards = activeFilter
    ? cards?.filter((card) => card.tag === activeFilter)
    : cards;

  // sort cards alphabetically
  const sortedCards = filteredCards?.sort((a, b) =>
    a.label.localeCompare(b.label)
  );

  const updatedFilters = filters ? [{ id: "", label: "All" }, ...filters] : [];

  return (
    <div className="mt-6" id={id}>
      <div className="flex flex-col items-start justify-between lg:flex-row lg:items-center">
        <h3 className="text-lg font-bold">{label}</h3>
        <FilterBar
          filters={updatedFilters}
          selectedId={activeFilter}
          onSelectFilter={handleSelectFilter}
        />
      </div>
      <div className="mt-5 flex gap-5 overflow-auto sm:flex-wrap">
        {sortedCards?.map((card) => (
          <IntegrationCard key={card.id} data={card} />
        ))}
        {!filteredCards?.length && (
          <span className="mt-4">No data exists for the current filter</span>
        )}
      </div>
    </div>
  );
};

type FilterBarProps = {
  filters: LabeledEntity[];
  selectedId: string;
  onSelectFilter: (filterId: string) => void;
};
const FilterBar = ({ filters, selectedId, onSelectFilter }: FilterBarProps) => {
  return (
    <div className="mt-2 flex max-w-full items-center lg:mt-0">
      <span className="font-semibold">Show</span>
      <div className="ml-3 flex overflow-auto rounded-md border border-gray-200 p-1">
        {filters.map((filter) => (
          <button
            key={filter.id}
            className={cn(
              "whitespace-nowrap border border-transparent px-2 py-1 text-sm font-medium",
              selectedId === filter.id &&
                "rounded-sm border-primary bg-primary-foreground"
            )}
            onClick={() => onSelectFilter(filter.id)}
          >
            {filter.label}
          </button>
        ))}
      </div>
    </div>
  );
};

function getIntegrationIcon(id: string) {
  switch (id) {
    case "connect_ms_calendar":
      return { icon: OutlookIcon };
    case "connect_microsoft_teams":
      return { icon: MicrosoftTeamsIcon };
    case "connect_microsoft_dynamics":
      return { iconSrc: dynamics };
    case "connect_salesforce_contact_support":
      return { icon: SalesforceIcon };
    case "connect_salesforce":
      return { icon: SalesforceIcon };
    case "connect_wealthbox":
      return { icon: WealthboxIcon };
    case "connect_zoom":
      return { icon: ZoomIcon };
    case "connect_google_calendar":
      return { icon: GoogleCalendarIcon };
    case "connect_google_meet":
      return { icon: GoogleMeetIcon };
    case "connect_redtail":
      return { icon: RedtailLogo };
    case "connect_webex":
      return { icon: WebexIcon };
    case "connect_advisorengine":
      return { iconSrc: advisorEngine };
    case "amplify":
      return { iconSrc: amplify };
    case "bento":
      return { iconSrc: bento, hasWideLogo: true };
    case "black_diamond":
      return { iconSrc: blackDiamond };
    case "practifi":
      return { iconSrc: practifi };
    case "salentica_elements":
      return { iconSrc: salenticaElements };
    case "salentica_engage":
      return { iconSrc: salenticaEngage };
    case "xlr8":
      return { iconSrc: xlr8 };
    case "tamarac":
      return { iconSrc: tamarac };
    case "hubspot":
      return { iconSrc: hubspot };
    default:
      // eslint-disable-next-line no-console
      console.error(`Could not find icon for integration ${id}`);
      return { icon: () => null };
  }
}

type IntegrationCardType = {
  data: SectionItemIntegrationCard;
};
const IntegrationCard = ({ data }: IntegrationCardType) => {
  const {
    id,
    label,
    value,
    isActive,
    isInteractible,
    redirectPath,
    isNew,
    errorMsg,
    ctaId,
  } = data;

  let ctaText = value;
  if (!ctaText) {
    ctaText = isActive ? "Connected" : `Connect ${label}`;
  }

  const {
    icon: Icon = () => null,
    iconSrc,
    hasWideLogo,
  } = getIntegrationIcon(id);

  const onClick = async () => {
    if (!redirectPath) {
      return;
    }

    // show toast for errorMsg
    if (errorMsg) {
      toast.error(errorMsg, {
        toastId: `${id}-cta-error`,
      });
      return;
    }

    // most integrations do not involve `code_challenge`, so we redirect the user without additional logic
    if (!redirectPath.includes("{code_challenge}")) {
      window.location.href = redirectPath;
      return;
    }

    // generate a PKCE code; save it in cookies; then redirect the user to updated path
    const { code_verifier, code_challenge } = await pkceChallenge();
    document.cookie = `code_verifier_${id}=${code_verifier}; path=/; secure; samesite=lax`;

    const updatedRedirectPath = redirectPath.replace(
      "{code_challenge}",
      code_challenge
    );

    window.location.href = updatedRedirectPath;
  };

  return (
    <div
      className={cn(
        "relative w-64 shrink-0 rounded-lg border p-6 last-of-type:mr-0",
        isActive && "border-primary"
      )}
    >
      {isNew && !!redirectPath && (
        <span className="absolute right-[-1px] top-[-1px] rounded-lg bg-primary px-2 py-1 text-[.5rem] text-primary-foreground">
          NEW !
        </span>
      )}
      <div className="flex items-center justify-between">
        <h4>{label}</h4>
        {iconSrc ? (
          <img
            className={cn("h-8 w-8 object-contain", hasWideLogo && "w-14")}
            src={iconSrc}
            alt={`${label} logo`}
          />
        ) : (
          <Icon className="h-8 w-8" />
        )}
      </div>
      <div
        className={cn(
          "mt-2 text-sm text-muted-foreground",
          isActive && "text-success"
        )}
      >
        {isActive ? "Active integration" : "Not integrated"}
      </div>
      {isInteractible && (
        <Button
          variant={isActive ? "outline" : "outline_primary"}
          className={cn("mt-6 w-full text-sm")}
          onClick={onClick}
          id={ctaId || ""}
        >
          {ctaText}
        </Button>
      )}
    </div>
  );
};

export default IntegrationCards;
