import { SerializeFrom } from "~/types/remix";
import { ChartData } from "chart.js";
import { eachDayOfInterval, format } from "date-fns";
import {
  NoteInsightData,
  NoteInsightDataSourceEnum,
  ProcessingStatus,
  ScheduledEventInsightData,
  TaskInsightData,
} from "~/api/openapi/generated";

export interface Filter {
  startDate: Date;
  endDate: Date;
  clientUUIDs: Set<string>;
  advisorUUIDs: Set<string>;
}

export const unknownClientSegmentName = "Uncategorized";
export const unknownClientPhaseName = "Uncategorized";

// Returns the notes in `notes` that have attendees that match the client or advisor UUIDs in `filter`.
//
//// An empty filter item means that the notes should not be filtered by that item.
const filterNotesByClientAndAdvisor = (
  notes: NoteInsightData[],
  filter: Filter
) => {
  const { clientUUIDs, advisorUUIDs } = filter;
  let filteredNotes = notes;
  if (advisorUUIDs.size > 0) {
    filteredNotes = notes.filter((note) =>
      note.authorizedUserUuids.some((a) => advisorUUIDs.has(a))
    );
  }
  if (clientUUIDs.size > 0) {
    filteredNotes = notes.filter((note) =>
      note.clientUuids.some((c) => clientUUIDs.has(c))
    );
  }
  return filteredNotes;
};

const filterNotesForCompleted = (
  notes: NoteInsightData[]
): NoteInsightData[] => {
  return notes.filter(
    (note) =>
      note.status === ProcessingStatus.Processed ||
      note.status === ProcessingStatus.Finalized
  );
};

// Returns a chart data object for the number of notes by date.
export const getNotesByDate = (
  notes: NoteInsightData[],
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "EEE M/dd/yyyy")
  );

  const notesCountByDate: Record<string, number> = dateRange.reduce(
    (acc, date) => {
      acc[date] = 0;
      return acc;
    },
    {} as Record<string, number>
  );

  filterNotesForCompleted(filterNotesByClientAndAdvisor(notes, filter)).forEach(
    (note) => {
      const formattedDate = format(note.startTime, "EEE M/dd/yyyy");
      if (notesCountByDate[formattedDate] !== undefined) {
        notesCountByDate[formattedDate] += 1;
      }
    }
  );

  const nonEmptyDateRange = dateRange.filter(
    (date) => notesCountByDate[date] !== 0
  );

  return {
    labels: nonEmptyDateRange.map((date) => format(new Date(date), "EEE M/dd")),
    datasets: [
      {
        data: nonEmptyDateRange.map((date) => notesCountByDate[date] ?? 0),
      },
    ],
  };
};

// Returns a chart data object for the number of notes per advisor.
//
// Advisors are filtered by the `advisorUUIDs` in `filter`. They are read from the note's attendees,
// so one meeting can be counted for multiple advisors.
export const getNotesByAdvisor = (
  notes: NoteInsightData[],
  userUUIDToUserNameOrEmail: Record<string, string>,
  filter: Filter
): ChartData<"line"> => {
  const { startDate, endDate, advisorUUIDs } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "M/dd/yyyy")
  );

  const notesByOwner = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const formattedDate = format(new Date(note.startTime), "M/dd/yyyy");
      note.authorizedUserUuids.forEach((authorizedUserUuid) => {
        if (advisorUUIDs.size > 0 && !advisorUUIDs.has(authorizedUserUuid)) {
          return;
        }
        if (!userUUIDToUserNameOrEmail[authorizedUserUuid]) {
          return;
        }

        if (!acc[authorizedUserUuid]) {
          acc[authorizedUserUuid] = Object.fromEntries(
            dateRange.map((date) => [date, 0])
          );
        }
        if (acc[authorizedUserUuid]?.[formattedDate] !== undefined) {
          acc[authorizedUserUuid]![formattedDate] += 1;
        }
      });
      return acc;
    },
    {} as Record<string, Record<string, number>>
  );

  const datasets = Object.entries(notesByOwner).map(([owner, notes]) => ({
    label: userUUIDToUserNameOrEmail[owner],
    data: dateRange.map((date) => notes[date] ?? 0),
  }));

  return {
    labels: dateRange.map((date) => format(new Date(date), "M/dd")),
    datasets,
  };
};

// Returns a chart data object for the number of notes by input method (mic, video, phone).
export const getNotesByAudioSource = (
  notes: NoteInsightData[],
  filter: Filter
): ChartData<"pie"> => {
  const { startDate, endDate } = filter;
  const methodsCount = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        const source = note.source;
        acc[source] = (acc[source] ?? 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>
  );

  const methodMap: Record<string, string> = {
    [NoteInsightDataSourceEnum.Notetaker]: "Virtual (video call)",
    [NoteInsightDataSourceEnum.PhoneCall]: "Phone call",
    [NoteInsightDataSourceEnum.Mic]: "In-person",
    [NoteInsightDataSourceEnum.Unknown]: "Unknown",
  };

  const labels = Object.keys(methodMap).filter(
    (key) => methodsCount[key] !== undefined && methodsCount[key] > 0
  );
  const data = labels.map((label) => methodsCount[label] ?? 0);

  const total = data.reduce((acc, count) => acc + count, 0);
  const percentagesPerLabel = labels.reduce(
    (acc, label) => {
      acc[label] = Math.round(((methodsCount[label] ?? 0) / total) * 100);
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    labels: labels.map((label) => {
      let l = methodMap[label];
      const percentage = percentagesPerLabel[label];
      if (percentage && !isNaN(percentage)) {
        l += ` (${percentage}%)`;
      }
      return l;
    }),
    datasets: [{ data }],
  };
};

// Returns a chart data object for the number of notes by status.
export const getNotesByStatus = (
  notes: NoteInsightData[],
  filter: Filter
): ChartData<"pie"> => {
  const { startDate, endDate } = filter;
  const processingStatusToDisplay: Record<
    ProcessingStatus,
    string | undefined
  > = {
    [ProcessingStatus.Scheduled]: undefined,
    [ProcessingStatus.Uploaded]: undefined,
    [ProcessingStatus.Unknown]: undefined,
    [ProcessingStatus.Processed]: "Pending Review",
    [ProcessingStatus.Finalized]: "Synced",
  };

  const statusCount: Record<string, number> = filterNotesByClientAndAdvisor(
    notes,
    filter
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        let status = processingStatusToDisplay[note.status];
        if (note.tags.map((t) => t.name).includes("Empty note")) {
          status = "Empty";
        }
        if (!status) {
          return acc;
        }
        acc[status] = (acc[status] ?? 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>
  );

  const labels = Object.keys(statusCount);
  const data = labels.map((label) => statusCount[label] || 0);

  const total = data.reduce((acc, count) => acc + count, 0);
  const percentagesPerLabel = labels.reduce(
    (acc, label) => {
      acc[label] = Math.round(((statusCount[label] ?? 0) / total) * 100);
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    labels: labels.map((label) => `${label} (${percentagesPerLabel[label]}%)`),
    datasets: [{ data }],
  };
};

export const getTasksByAssignee = (
  tasks: TaskInsightData[],
  userUUIDToUserNameOrEmail: Record<string, string>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const tasksByAssignee = tasks
    .filter((task) => {
      const filterUUIDs = filter.clientUUIDs;
      for (const advisorUUID of filter.advisorUUIDs) {
        filterUUIDs.add(advisorUUID);
      }
      if (filterUUIDs.size == 0) {
        return true;
      }

      return task.assigneeUuid && filterUUIDs.has(task.assigneeUuid)
        ? true
        : false;
    })
    .reduce(
      (acc, task) => {
        const taskDate = task.creationDate;
        if (taskDate >= startDate && taskDate <= endDate) {
          const assignee =
            (task.assigneeUuid &&
              userUUIDToUserNameOrEmail[task.assigneeUuid]) ??
            "No assignee specified";
          acc[assignee] = (acc[assignee] ?? 0) + 1;
        }
        return acc;
      },
      {} as Record<string, number>
    );

  const labels = Object.keys(tasksByAssignee);
  const data = labels.map((label) => tasksByAssignee[label] || 0);

  return {
    labels,
    datasets: [{ data }],
  };
};

// Returns a chart data object for the total duration of notes by advisor and day.
export const getTotalDurationByAdvisorAndDay = (
  notes: NoteInsightData[],
  userUUIDToUserNameOrEmail: Record<string, string>,
  filter: Filter
): ChartData<"line"> => {
  const { startDate, endDate, advisorUUIDs } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "M/dd/yyyy")
  );

  const durationByAdvisorAndDay = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const formattedDate = format(note.startTime, "M/dd/yyyy");
      const duration = note.durationSeconds || 0;

      note.authorizedUserUuids.forEach((authorizedUserUUID) => {
        if (advisorUUIDs.size > 0 && !advisorUUIDs.has(authorizedUserUUID)) {
          return;
        }

        if (!acc[authorizedUserUUID]) {
          acc[authorizedUserUUID] = {
            name:
              userUUIDToUserNameOrEmail[authorizedUserUUID] ??
              "Unknown Advisor",
            durations: Object.fromEntries(dateRange.map((date) => [date, 0])),
          };
        }

        if (acc[authorizedUserUUID]?.durations[formattedDate] !== undefined) {
          acc[authorizedUserUUID]!.durations[formattedDate]! += duration;
        }
      });

      return acc;
    },
    {} as Record<string, { name: string; durations: Record<string, number> }>
  );

  const datasets = Object.values(durationByAdvisorAndDay).map(
    ({ name, durations }) => ({
      label: name,
      data: dateRange.map((date) => Math.round((durations[date] ?? 0) / 3600)),
    })
  );

  return {
    labels: dateRange.map((date) => format(new Date(date), "M/dd")),
    datasets,
  };
};

export const getClientCountByOwner = (
  notes: NoteInsightData[],
  userUUIDToUserNameOrEmail: Record<string, string>,
  clientUUIDToName: Record<string, string>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const clientCountByOwner = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        note.authorizedUserUuids.forEach((advisorUUID) => {
          if (!acc[advisorUUID]) {
            acc[advisorUUID] = {
              name: userUUIDToUserNameOrEmail[advisorUUID] ?? "Unknown Advisor",
              clients: new Set<string>(),
            };
          }

          note.clientUuids.forEach((clientUuid) => {
            acc[advisorUUID]?.clients.add(
              clientUUIDToName[clientUuid] ?? "Unnamed client"
            );
          });
        });
      }
      return acc;
    },
    {} as Record<string, { name: string; clients: Set<string> }>
  );

  const result = Object.entries(clientCountByOwner).reduce(
    (acc, [ownerUuid, { name, clients }]) => {
      acc[ownerUuid] = { name, clientCount: clients.size };
      return acc;
    },
    {} as Record<string, { name: string; clientCount: number }>
  );

  const labels = Object.values(result).map((owner) => owner.name);
  const data = Object.values(result).map((owner) => owner.clientCount);

  return {
    labels,
    datasets: [
      {
        data,
      },
    ],
  };
};

export const getTagCounts = (notes: NoteInsightData[], filter: Filter) => {
  const { startDate, endDate } = filter;
  const filteredNotes = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  );

  const tagData = filteredNotes.reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        note.tags.forEach((tag) => {
          if (!acc[tag.name]) {
            acc[tag.name] = { count: 0, percentage: "0" };
          }
          acc[tag.name]!.count += 1;
        });
      }
      return acc;
    },
    {} as Record<string, { count: number; percentage: string }>
  );

  Object.entries(tagData).forEach(
    ([_, data]) =>
      (data.percentage = ((data.count / filteredNotes.length) * 100).toFixed(1))
  );

  return tagData;
};

export const getTagsByClient = (
  notes: NoteInsightData[],
  clientUUIDToName: Record<string, string>,
  filter: Filter
) => {
  const { startDate, endDate } = filter;
  const tagsByClient = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        note.clientUuids.forEach((clientUUID) => {
          if (!acc[clientUUID]) {
            acc[clientUUID] = {
              name: clientUUIDToName[clientUUID] ?? "Unnamed client",
              tags: {},
            };
          }
          note.tags.forEach((tag) => {
            acc[clientUUID]!.tags[tag.name] =
              (acc[clientUUID]?.tags[tag.name] ?? 0) + 1;
          });
        });
      }
      return acc;
    },
    {} as Record<string, { name: string; tags: Record<string, number> }>
  );

  return Object.entries(tagsByClient).reduce(
    (acc, [clientUuid, { name, tags }]) => {
      const sortedTags = Object.entries(tags).sort(
        ([nameOne, countOne], [nameTwo, countTwo]) => {
          const countDiff = countTwo - countOne;
          return countDiff !== 0 ? countDiff : nameOne.localeCompare(nameTwo);
        }
      );
      acc[clientUuid] = { name, tags: sortedTags.map(([tag]) => tag) };
      return acc;
    },
    {} as Record<string, { name: string; tags: string[] }>
  );
};

export const getNotesAndScheduledEventCounts = (
  notes: NoteInsightData[],
  scheduledEvents: ScheduledEventInsightData[],
  filter: Filter
): ChartData<"pie"> => {
  const clientScheduledEvents = scheduledEvents.filter((event) =>
    filter.advisorUUIDs.size > 0
      ? filter.advisorUUIDs.has(event.userUuid)
      : true
  );
  const clientScheduledEventsWithNotesCount = clientScheduledEvents.filter(
    (event) =>
      notes.some(
        (note) =>
          note.scheduledEventUuid === event.scheduledEventUuid &&
          // Only count notes that have had some evidence of Zeplyn processing.
          (note.status === ProcessingStatus.Finalized ||
            note.status === ProcessingStatus.Processed ||
            note.status === ProcessingStatus.Uploaded)
      )
  ).length;
  return {
    labels: ["Without Zeplyn", "With Zeplyn"],
    datasets: [
      {
        data: [
          clientScheduledEvents.length - clientScheduledEventsWithNotesCount,
          clientScheduledEventsWithNotesCount,
        ],
      },
    ],
  };
};

export const getTopLineMetrics = (notes: NoteInsightData[], filter: Filter) => {
  const filteredNotes = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).filter(
    (note) =>
      note.startTime >= filter.startDate && note.startTime <= filter.endDate
  );
  return {
    notesCount: filteredNotes.length,
    notesSyncedToCRM: filteredNotes.filter(
      (n) => n.status === ProcessingStatus.Finalized
    ).length,
    followUpsGenerated: filteredNotes.filter((n) => n.followUpGenerated).length,
    timeSavedMinutes:
      filteredNotes.filter((n) => n.status === ProcessingStatus.Finalized)
        .length *
        90 +
      filteredNotes.filter((n) => n.status === ProcessingStatus.Processed)
        .length *
        60,
  };
};

export const getAverageDurationByClientSegment = (
  notes: NoteInsightData[],
  clientUUIDToClientSegment: Record<string, string>,
  segmentsToLabels: Record<string, string>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const durationByClientSegment = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        const duration = note.durationSeconds || 0;
        note.clientUuids.forEach((clientUuid) => {
          const segment =
            clientUUIDToClientSegment[clientUuid] || unknownClientSegmentName;
          if (!acc[segment]) {
            acc[segment] = {
              totalDuration: 0,
              count: 0,
            };
          }
          acc[segment].totalDuration += duration;
          acc[segment].count += 1;
        });
      }
      return acc;
    },
    {} as Record<string, { totalDuration: number; count: number }>
  );
  const result = Object.entries(durationByClientSegment).reduce(
    (acc, [segment, { totalDuration, count }]) => {
      acc[segment] = {
        name: segment,
        averageDuration: Math.round(
          (count > 0 ? totalDuration / count : 0) / 60
        ),
      };
      return acc;
    },
    {} as Record<string, { name: string; averageDuration: number }>
  );

  return {
    labels: Object.values(result).map(
      (segment) => segmentsToLabels[segment.name] || segment.name
    ),
    datasets: [
      {
        label: "Average Duration",
        data: Object.values(result).map((segment) => segment.averageDuration),
      },
    ],
  };
};

export const getTotalDurationByClientSegmentByDay = (
  notes: NoteInsightData[],
  clientUUIDToClientSegment: Record<string, string>,
  segmentsToLabels: Record<string, string>,
  filter: Filter
): ChartData<"line"> => {
  const { startDate, endDate } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "M/dd/yyyy")
  );
  const durationByClientSegment = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const formattedDate = format(note.startTime, "M/dd/yyyy");
      const duration = note.durationSeconds || 0;
      note.clientUuids.forEach((clientUuid) => {
        const segment =
          clientUUIDToClientSegment[clientUuid] || unknownClientSegmentName;
        if (!acc[segment]) {
          acc[segment] = Object.fromEntries(dateRange.map((date) => [date, 0]));
        }
        if (acc[segment]?.[formattedDate] !== undefined) {
          acc[segment]![formattedDate]! += duration;
        }
      });
      return acc;
    },
    {} as Record<string, Record<string, number>>
  );

  const datasets = Object.entries(durationByClientSegment).map(
    ([segment, durations]) => ({
      label: segmentsToLabels[segment] || segment,
      data: dateRange.map((date) => Math.round((durations[date] ?? 0) / 3600)),
    })
  );
  return {
    labels: dateRange.map((date) => format(new Date(date), "M/dd")),
    datasets,
  };
};

export const getNotesByClientSegment = (
  notes: NoteInsightData[],
  clientUUIDToClientSegment: Record<string, string>,
  segmentsToLabels: Record<string, string>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const notesByClientSegment = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        const segmentsInNote = new Set<string>();
        note.clientUuids.forEach((clientUuid) => {
          if (!clientUUIDToClientSegment[clientUuid]) {
            return;
          }
          const segment =
            segmentsToLabels[clientUUIDToClientSegment[clientUuid]] ||
            unknownClientSegmentName;
          segmentsInNote.add(segment);
        });
        segmentsInNote.forEach((segment) => {
          acc[segment] = (acc[segment] ?? 0) + 1;
        });
      }
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    labels: Object.keys(notesByClientSegment),
    datasets: [
      {
        label: "Notes Count",
        data: Object.values(notesByClientSegment),
      },
    ],
  };
};

export const getNotesByMeetingPrepGenerated = (
  notes: NoteInsightData[],
  filter: Filter
): ChartData<"pie"> => {
  const { startDate, endDate } = filter;
  const notesCount = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      if (noteDate >= startDate && noteDate <= endDate) {
        acc[note.meetingPrepGenerated ? "With Prep" : "Without Prep"] =
          (acc[note.meetingPrepGenerated ? "With Prep" : "Without Prep"] ?? 0) +
          1;
      }
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    labels: Object.keys(notesCount),
    datasets: [{ data: Object.values(notesCount) }],
  };
};

export const getClientsByLifePhase = (
  clientUUIDToLifePhase: Record<string, string>,
  phasesToLabels: Record<string, string>,
  filter: Filter
): ChartData<"pie"> => {
  const { clientUUIDs } = filter;

  const filteredClientUUIDs =
    clientUUIDs.size > 0
      ? Array.from(clientUUIDs)
      : Object.keys(clientUUIDToLifePhase);

  const lifePhaseCount: Record<string, number> = filteredClientUUIDs.reduce(
    (acc, clientUUID) => {
      if (!clientUUIDToLifePhase[clientUUID]) {
        return acc;
      }
      const phase =
        phasesToLabels[clientUUIDToLifePhase[clientUUID]] ||
        unknownClientPhaseName;
      if (phase) {
        acc[phase] = (acc[phase] ?? 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    labels: Object.keys(lifePhaseCount),
    datasets: [{ data: Object.values(lifePhaseCount) }],
  };
};

export const getMeetingCountByClientByCalendarYear = (
  notes: NoteInsightData[],
  clientUUIDToName: Record<string, string>,
  filter: Filter
): {
  years: number[];
  data: Record<
    string,
    { id: string; name: string; countsByYear: Record<number, number> }
  >;
} => {
  // Note that this does not filter by date range, since it's specifically aggregating over a long
  // range of dates.
  const years = new Set<number>();
  const notesByClient = filterNotesForCompleted(
    filterNotesByClientAndAdvisor(notes, filter)
  ).reduce(
    (acc, note) => {
      const noteDate = note.startTime;
      note.clientUuids.forEach((clientUUID) => {
        const year = noteDate.getFullYear();
        years.add(year);

        if (!acc[clientUUID]) {
          acc[clientUUID] = {
            id: clientUUID,
            name: clientUUIDToName[clientUUID] || "Unnamed client",
            countsByYear: {},
          };
        }
        acc[clientUUID].countsByYear[year] =
          (acc[clientUUID].countsByYear[year] || 0) + 1;
      });
      return acc;
    },
    {} as Record<
      string,
      { id: string; name: string; countsByYear: Record<number, number> }
    >
  );

  return { years: [...years].sort((a, b) => b - a), data: notesByClient };
};
