import {
  Await,
  LoaderFunctionArgs,
  MetaFunction,
  redirect,
} from "react-router";
import { Link, useLoaderData } from "react-router";
import {
  addDays,
  endOfDay,
  format,
  isAfter,
  isBefore,
  parse,
  startOfDay,
} from "date-fns";
import { Suspense, useMemo, useState } from "react";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { Badge } from "~/@shadcn/ui/badge";
import { Typography } from "~/@ui/Typography";
import {
  Configuration,
  InsightsApi,
  InsightsDashboardResponse,
  LifePhase,
  Segment,
  Status,
} from "~/api/openapi/generated";
import { Bar, Line, Pie } from "react-chartjs-2";
import { ClientOnly } from "remix-utils/client-only";
import "chart.js/auto";
import { Chart } from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { configurationParameters } from "~/api/openapi/configParams";
import { But<PERSON> } from "~/@shadcn/ui/button";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import {
  getClientCountByOwner,
  getNotesByDate,
  getNotesByAudioSource,
  getNotesByAdvisor,
  getNotesByStatus,
  getTagCounts,
  getTagsByClient,
  getTasksByAssignee,
  getTotalDurationByAdvisorAndDay,
  Filter,
  getNotesAndScheduledEventCounts,
  getTopLineMetrics,
  getAverageDurationByClientSegment,
  getNotesByClientSegment,
  getTotalDurationByClientSegmentByDay,
  getNotesByMeetingPrepGenerated,
  getClientsByLifePhase,
  unknownClientSegmentName,
  unknownClientPhaseName,
  getMeetingCountByClientByCalendarYear,
} from "./dataProcessing";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import "./insights-overrides.css";
import GridLayout from "react-grid-layout";
import { withBarColors, withLineColors, withPieColors } from "./colors";
import { Download, ListFilter } from "lucide-react";
import { cn } from "~/@shadcn/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "~/@shadcn/ui/popover";
import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";
import { VirtualizedMultiSelect } from "~/@ui/VirtualizedMultiSelect";
import DataTable, { TableColumn } from "react-data-table-component";

Chart.register(ChartDataLabels);

export const meta: MetaFunction = () => [
  { title: "Insights" },
  {
    name: "description",
    content: "View insights about your practice and clients",
  },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const configuration = new Configuration(
    await configurationParameters(request)
  );
  const insightsPromise = new InsightsApi(configuration)
    .insightsGetInsightsData()
    .catch((error) => {
      if (error.response?.status === 401) {
        throw redirect("/auth/logout");
      }
      return undefined;
    });

  return { insightsPromise };
};

// A skeleton to show before the page has loaded.
const Skeletons = () => (
  <div className="flex w-[1620px] flex-col space-y-4">
    <Skeleton className="h-16 w-full" />
    <div className="grid w-full grid-cols-3 gap-[20px]">
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="asect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
    </div>
  </div>
);

const ChartTitle = ({
  title,
  onDownload,
  children,
}: {
  title: string;
  children?: React.ReactNode;
  onDownload: () => void;
}) => {
  return (
    <div className="flex flex-row items-center justify-between">
      <div className="flex flex-row items-center gap-2">
        <Typography className="tracking-wide" variant="h4">
          {title}
        </Typography>
        {children}
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="icon-sm" onClick={onDownload}>
              <Download />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Download data</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

interface MeetingsByClientByYearRowType {
  id: string;
  name: string;
  countsByYear: Record<number, number>;
}

// Displays an editable grid of charts/dashboards.
const Charts = ({
  insights,
  context,
}: {
  insights: InsightsDashboardResponse;
  context: "practice" | "client" | "clientGroupings";
}) => {
  const { notes, tasks, scheduledEvents, users, clients } = insights;
  const today = new Date();
  const oneYearAgo = addDays(today, -365);
  const [endDate, setEndDate] = useState(endOfDay(today));
  const [startDate, setStartDate] = useState(startOfDay(addDays(today, -7)));

  const defaultSize = useMemo(() => ({ w: 6, h: 3 }), []);
  const layouts: Record<Tab, GridLayout.Layout[]> = useMemo(
    () => ({
      practice: [
        { x: 6 * 0, y: 3 * 0, ...defaultSize, i: "meetings-per-day" },
        { x: 6 * 1, y: 3 * 0, ...defaultSize, i: "meetings-using-zeplyn" },
        { x: 6 * 2, y: 3 * 0, ...defaultSize, i: "meeting-method" },
        { x: 6 * 0, y: 3 * 1, ...defaultSize, i: "meeting-outcomes" },
        { x: 6 * 1, y: 3 * 1, ...defaultSize, i: "tasks-assigned" },
        { x: 6 * 2, y: 3 * 1, ...defaultSize, i: "usage-by-advisor" },
        { x: 6 * 0, y: 3 * 2, ...defaultSize, i: "scheduled-events" },
        { x: 6 * 1, y: 3 * 2, ...defaultSize, i: "meeting-prep-generated" },
      ],
      client: [
        { x: 9 * 0, y: 3 * 0, w: 9, h: 3, i: "topics", isResizable: false },
        {
          x: 9 * 1,
          y: 3 * 0,
          w: 9,
          h: 3,
          i: "client-topics",
          isResizable: false,
        },
        { x: 6 * 0, y: 3 * 1, w: 12, h: 12, i: "meetings-by-client-by-year" },
        { x: 6 * 2, y: 3 * 1, ...defaultSize, i: "client-count" },
      ],
      clientGroupings: [
        {
          x: 6 * 0,
          y: 3 * 0,
          ...defaultSize,
          i: "notes-by-client-segment",
        },
        {
          x: 6 * 1,
          y: 3 * 0,
          ...defaultSize,
          i: "usage-by-client-segment",
        },
        {
          x: 6 * 2,
          y: 3 * 0,
          ...defaultSize,
          i: "average-duration-by-client-segment",
        },
        {
          x: 6 * 0,
          y: 3 * 1,
          ...defaultSize,
          i: "notes-by-client-phase",
        },
        {
          x: 6 * 0,
          y: 3 * 2,
          ...defaultSize,
          i: "clients-by-life-phase",
        },
      ],
    }),
    [defaultSize]
  );

  const layout = useMemo(() => layouts[context], [layouts, context]);
  const activeCharts = new Set(layout.map((item) => item.i));

  // Given a layout for a grid, returns a layout with the `static` property of each item set to
  // `true` (i.e., makes the grid un-editable).
  const updateLayoutWithStatic = (layout: GridLayout.Layout[]) =>
    layout.map((item) => ({ ...item, static: true }));

  const userUUIDToUserNameOrEmail = useMemo(
    () =>
      users.reduce(
        (acc, user) => {
          acc[user.uuid] = user.name ?? user.email;
          return acc;
        },
        {} as Record<string, string>
      ),
    [users]
  );

  const clientUUIDToClientName = useMemo(
    () =>
      clients.reduce(
        (acc, client) => {
          acc[client.uuid] = client.name;
          return acc;
        },
        {} as Record<string, string>
      ),
    [clients]
  );

  const [selectedAdvisors, setSelectedAdvisors] = useState<string[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [selectedClientSegments, setSelectedClientSegments] = useState<
    string[]
  >([]);
  const [selectedClientPhases, setSelectedClientPhases] = useState<string[]>(
    []
  );
  const [minimumAUM, setMinimumAUM] = useState<number | undefined>(undefined);
  const [maximumAUM, setMaximumAUM] = useState<number | undefined>(undefined);

  const [showTagPercentages, setShowTagPercentages] = useState(false);

  const clientSegmentToClients = useMemo(
    () =>
      clients.reduce(
        (acc, client) => {
          const segment = client.segment || unknownClientSegmentName;
          if (!acc[segment]) {
            acc[segment] = [];
          }
          acc[segment].push(client);
          return acc;
        },
        {} as Record<string, typeof clients>
      ),
    [clients]
  );

  const aumMatchedClients = useMemo(() => {
    if (minimumAUM === undefined || maximumAUM === undefined) {
      return [];
    }
    return clients.reduce(
      (acc, client) => {
        if (
          client.assetsUnderManagement &&
          Number(client.assetsUnderManagement) >= minimumAUM &&
          Number(client.assetsUnderManagement) <= maximumAUM
        ) {
          acc.push(client);
        }
        return acc;
      },
      [] as typeof clients
    );
  }, [clients, minimumAUM, maximumAUM]);

  const clientPhaseToClients = useMemo(
    () =>
      clients.reduce(
        (acc, client) => {
          const phase = client.lifePhase || unknownClientPhaseName;
          if (!acc[phase]) {
            acc[phase] = [];
          }
          acc[phase].push(client);
          return acc;
        },
        {} as Record<string, typeof clients>
      ),
    [clients]
  );

  const clientUUIDToClientSegment = useMemo(
    () =>
      clients.reduce(
        (acc, client) => {
          acc[client.uuid] = client.segment || unknownClientSegmentName;
          return acc;
        },
        {} as Record<string, string>
      ),
    [clients]
  );

  const clientUUIDToLifePhase = useMemo(
    () =>
      clients.reduce(
        (acc, client) => {
          acc[client.uuid] = client.lifePhase || unknownClientPhaseName;
          return acc;
        },
        {} as Record<string, string>
      ),
    [clients]
  );

  const aggregatedClients = useMemo(() => {
    let clients = selectedClients;
    clients = clients.concat(
      selectedClientSegments.flatMap(
        (segment) =>
          clientSegmentToClients[segment]?.map((client) => client.uuid) || []
      )
    );
    clients = clients.concat(
      selectedClientPhases.flatMap(
        (phase) =>
          clientPhaseToClients[phase]?.map((client) => client.uuid) || []
      )
    );
    clients = clients.concat(aumMatchedClients.map((client) => client.uuid));
    return new Set(clients);
  }, [
    selectedClients,
    selectedClientSegments,
    clientSegmentToClients,
    selectedClientPhases,
    clientPhaseToClients,
    aumMatchedClients,
  ]);

  const segmentsToLabels: Record<string, string> = useMemo(
    () => ({
      [Segment.Bronze.valueOf()]: "Bronze",
      [Segment.Silver.valueOf()]: "Silver",
      [Segment.Gold.valueOf()]: "Gold",
      [Segment.Platinum.valueOf()]: "Platinum",
    }),
    []
  );

  const phasesToLabels: Record<string, string> = useMemo(
    () => ({
      [LifePhase.Accumulation.valueOf()]: "Accumulation",
      [LifePhase.Consolidation.valueOf()]: "Consolidation",
      [LifePhase.Distribution.valueOf()]: "Distribution",
      [LifePhase.Retirement.valueOf()]: "Retirement",
    }),
    []
  );

  // Memoize the data processing functions to avoid re-computing them on every render.
  const filter: Filter = useMemo(
    () => ({
      startDate,
      endDate,
      advisorUUIDs: new Set(selectedAdvisors),
      clientUUIDs: new Set(aggregatedClients),
    }),
    [startDate, endDate, selectedAdvisors, aggregatedClients]
  );

  const countsByTag = useMemo(
    () => getTagCounts(notes, filter),
    [notes, filter]
  );
  const tagsByClient = useMemo(
    () => getTagsByClient(notes, clientUUIDToClientName, filter),
    [notes, clientUUIDToClientName, filter]
  );
  const notesByDate = useMemo(
    () => withBarColors(getNotesByDate(notes, filter)),
    [notes, filter]
  );
  const notesByOwner = useMemo(
    () =>
      withLineColors(
        getNotesByAdvisor(notes, userUUIDToUserNameOrEmail, filter)
      ),
    [notes, userUUIDToUserNameOrEmail, filter]
  );
  const notesByAudioSource = useMemo(
    () => withPieColors(getNotesByAudioSource(notes, filter)),
    [notes, filter]
  );
  const notesByStatus = useMemo(
    () => withPieColors(getNotesByStatus(notes, filter)),
    [notes, filter]
  );
  const tasksByAssignee = useMemo(
    () =>
      withBarColors(
        getTasksByAssignee(tasks, userUUIDToUserNameOrEmail, filter)
      ),
    [tasks, userUUIDToUserNameOrEmail, filter]
  );
  const durationByAdvisorAndDay = useMemo(
    () =>
      withLineColors(
        getTotalDurationByAdvisorAndDay(
          notes,
          userUUIDToUserNameOrEmail,
          filter
        )
      ),
    [notes, userUUIDToUserNameOrEmail, filter]
  );
  const clientCountByOwner = useMemo(
    () =>
      withBarColors(
        getClientCountByOwner(
          notes,
          userUUIDToUserNameOrEmail,
          clientUUIDToClientName,
          filter
        )
      ),
    [notes, userUUIDToUserNameOrEmail, clientUUIDToClientName, filter]
  );
  const notesByClientSegment = useMemo(
    () =>
      withBarColors(
        getNotesByClientSegment(
          notes,
          clientUUIDToClientSegment,
          segmentsToLabels,
          filter
        )
      ),
    [notes, clientUUIDToClientSegment, segmentsToLabels, filter]
  );
  const durationByClientSegment = useMemo(
    () =>
      withLineColors(
        getTotalDurationByClientSegmentByDay(
          notes,
          clientUUIDToClientSegment,
          segmentsToLabels,
          filter
        )
      ),
    [notes, clientUUIDToClientSegment, segmentsToLabels, filter]
  );
  const notesByPrepGenerated = useMemo(
    () => withPieColors(getNotesByMeetingPrepGenerated(notes, filter)),
    [notes, filter]
  );
  const clientsByLifePhase = useMemo(
    () =>
      withPieColors(
        getClientsByLifePhase(clientUUIDToLifePhase, phasesToLabels, filter)
      ),
    [clientUUIDToLifePhase, phasesToLabels, filter]
  );
  const meetingsByClientByYear = useMemo(
    () =>
      getMeetingCountByClientByCalendarYear(
        notes,
        clientUUIDToClientName,
        filter
      ),
    [notes, clientUUIDToClientName, filter]
  );

  const downloadCSVData = (csvContent: string, filename: string) => {
    const blob = new Blob([csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Returns a constructed value for a `layout` for a Chart.js chart's options.
  const makeLayout = (rightPadding: number = 0) => {
    return {
      padding: {
        top: 30,
        right: rightPadding,
      },
    };
  };

  /// Returns a constructed value for a Chart.js chart options' `elements` for a bar graph.
  const makeBarElements = () => ({
    bar: { borderRadius: 4 },
  });

  /// Returns a constructed value for a Chart.js chart options' `scales`.
  const makeScales = ({
    showXTicks = true,
    showYGrid = false,
    showYTicks = false,
  } = {}) => {
    return {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          display: showXTicks,
        },
      },
      y: {
        grid: {
          display: showYGrid,
        },
        ticks: {
          display: showYTicks,
          precision: 0,
        },
      },
    };
  };

  // Fills an array with a given item to make its length a multiple of a given length.
  function fillToMultipleOfLength<T>(array: T[], length: number, item: T) {
    if (array.length % length === 0) {
      return array;
    }
    const fillLength = length - (array.length % length);
    return [...array, ...new Array(fillLength).fill(item)];
  }

  const averageDurationByClientSegment = useMemo(
    () =>
      withBarColors(
        getAverageDurationByClientSegment(
          notes,
          clientUUIDToClientSegment,
          segmentsToLabels,
          filter
        )
      ),
    [notes, clientUUIDToClientSegment, segmentsToLabels, filter]
  );

  const notesAndScheduledEventCounts = useMemo(
    () =>
      withPieColors(
        getNotesAndScheduledEventCounts(notes, scheduledEvents, filter)
      ),
    [notes, scheduledEvents, filter]
  );

  const topLineMetrics = useMemo(
    () => getTopLineMetrics(notes, filter),
    [notes, filter]
  );

  return (
    <div className="h-full w-full overflow-x-auto overflow-y-visible">
      {/* Filter popover */}
      <div className="flex w-full flex-col items-center justify-start gap-4 py-4 md:flex-row">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline">
              <ListFilter className="!h-5 !w-5" />
              Filter
            </Button>
          </PopoverTrigger>
          <PopoverContent className="flex w-auto flex-col gap-2" side="right">
            {/* Date range */}
            <Typography variant="h5">Date range</Typography>
            <div className="flex flex-row items-center gap-2">
              <input
                type="date"
                placeholder="Start date"
                value={format(startDate, "yyyy-MM-dd")}
                onChange={(e) => {
                  const newStartDate = parse(
                    e.target.value,
                    "yyyy-MM-dd",
                    new Date()
                  );
                  if (isNaN(newStartDate.getTime())) {
                    return;
                  }
                  if (isAfter(newStartDate, today)) {
                    return;
                  }
                  if (isAfter(newStartDate, endDate)) {
                    setEndDate(endOfDay(newStartDate));
                  }
                  setStartDate(startOfDay(newStartDate));
                }}
                max={format(today, "yyyy-MM-dd")}
                min={format(oneYearAgo, "yyyy-MM-dd")}
                className="rounded-md border border-gray-200 p-2"
              />
              <Typography variant="h5">-</Typography>
              <input
                type="date"
                placeholder="End date"
                value={format(endDate, "yyyy-MM-dd")}
                onChange={(e) => {
                  const newEndDate = parse(
                    e.target.value,
                    "yyyy-MM-dd",
                    new Date()
                  );
                  if (isNaN(newEndDate.getTime())) {
                    return;
                  }
                  if (isAfter(newEndDate, today)) {
                    return;
                  }
                  if (isBefore(newEndDate, startDate)) {
                    setStartDate(endOfDay(newEndDate));
                  }
                  setEndDate(startOfDay(newEndDate));
                }}
                max={format(today, "yyyy-MM-dd")}
                min={format(oneYearAgo, "yyyy-MM-dd")}
                className="rounded-md border border-gray-200 p-2"
              />
            </div>

            {/* Filter by advisor */}
            <div className="flex flex-col gap-1">
              <Typography variant="h5">Advisor</Typography>
              <VirtualizedMultiSelect
                placeholder="Advisor"
                options={users
                  .sort(
                    (
                      { name: nameOne, email: emailOne },
                      { name: nameTwo, email: emailTwo }
                    ) =>
                      (nameOne ?? emailOne).localeCompare(nameTwo ?? emailTwo)
                  )
                  .map((u) => ({
                    value: u.uuid,
                    label: u.name ?? u.email,
                  }))}
                selected={selectedAdvisors}
                onChange={(advisors) => {
                  setSelectedAdvisors(advisors);
                }}
              />
            </div>

            {/* Filter by client */}
            <div className="flex flex-col gap-1">
              <Typography variant="h5">Client</Typography>
              <VirtualizedMultiSelect
                placeholder="Client"
                options={clients
                  .sort(
                    ({ name: nameOne }, { name: nameTwo }) =>
                      nameOne.localeCompare(nameTwo) // sort by client name
                  )
                  .map((c) => ({ value: c.uuid, label: c.name }))}
                selected={selectedClients}
                onChange={(clients) => {
                  setSelectedClients(clients);
                }}
              />
            </div>
            {/* Filter by client segment */}
            <div className="flex flex-col gap-1">
              <Typography variant="h5">Client Segment</Typography>
              <VirtualizedMultiSelect
                placeholder="Client Segment"
                options={Object.keys(clientSegmentToClients)
                  .sort((a, b) => a.localeCompare(b)) // sort by segment name
                  .map((segment) => ({
                    value: segment,
                    label: segmentsToLabels[segment] || segment,
                  }))}
                selected={selectedClientSegments}
                onChange={(segments) => {
                  setSelectedClientSegments(segments);
                }}
              />
            </div>

            {/* Filter by client phase */}
            <div className="flex flex-col gap-1">
              <Typography variant="h5">Client Phase</Typography>
              <VirtualizedMultiSelect
                placeholder="Client Phase"
                options={Object.keys(clientPhaseToClients)
                  .sort((a, b) => a.localeCompare(b)) // sort by phase name
                  .map((phase) => ({
                    value: phase,
                    label: phasesToLabels[phase] || phase,
                  }))}
                selected={selectedClientPhases}
                onChange={(phases) => {
                  setSelectedClientPhases(phases);
                }}
              />
            </div>

            {/* Filter by AUM */}
            <div className="flex flex-col gap-1">
              <Typography variant="h5">
                Assets Under Management (AUM)
              </Typography>
              <div className="flex flex-col gap-2">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <span className="w-12 text-sm">Min:</span>
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        min="0"
                        step="10000"
                        value={minimumAUM || ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          const minimumAUM =
                            value === "" ? undefined : Number(value);
                          if (
                            minimumAUM !== undefined &&
                            maximumAUM !== undefined &&
                            minimumAUM > maximumAUM
                          ) {
                            setMaximumAUM(minimumAUM);
                          }
                          setMinimumAUM(
                            value === "" ? undefined : Number(value)
                          );
                        }}
                        placeholder="0"
                        className="flex-1 rounded-md border border-gray-200 p-2"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setMinimumAUM(undefined)}
                        disabled={minimumAUM === undefined}
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="w-12 text-sm">Max:</span>
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        min="0"
                        step="10000"
                        value={maximumAUM || ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          const maximumAUM =
                            value === "" ? undefined : Number(value);
                          if (
                            maximumAUM !== undefined &&
                            minimumAUM !== undefined &&
                            maximumAUM < minimumAUM
                          ) {
                            setMinimumAUM(maximumAUM);
                          }
                          setMaximumAUM(maximumAUM);
                        }}
                        placeholder="10000000"
                        className="flex-1 rounded-md border border-gray-200 p-2"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setMaximumAUM(undefined)}
                        disabled={maximumAUM === undefined}
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon-sm" asChild>
                <Link to="/feapi/insights/rawdata" reloadDocument>
                  <Download />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Download all data</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Top-level summary */}
      <div className="flex w-full flex-col items-center justify-start gap-4 rounded-md border p-4 md:flex-row">
        <div className="flex w-full flex-col items-center justify-start gap-4 md:flex-row">
          <div className="flex w-full flex-col items-center justify-start gap-2 md:w-1/4">
            <Typography variant="h5">Total meetings completed</Typography>
            <Badge className="text-2xl">
              {topLineMetrics.notesCount.toLocaleString()}
            </Badge>
          </div>
          <div className="flex w-full flex-col items-center justify-start gap-2 md:w-1/4">
            <Typography variant="h5">Email follow-ups generated</Typography>
            <Badge className="text-2xl">
              {topLineMetrics.followUpsGenerated}
            </Badge>
          </div>
          <div className="flex w-full flex-col items-center justify-start gap-2 md:w-1/4">
            <Typography variant="h5">CRM syncs</Typography>
            <Badge className="text-2xl">
              {topLineMetrics.notesSyncedToCRM.toLocaleString()}
            </Badge>
          </div>
          <div className="flex w-full flex-col items-center justify-start gap-2 md:w-1/4">
            <Typography variant="h5">Time saved</Typography>
            <Badge className="text-2xl">
              {(topLineMetrics.timeSavedMinutes / 60).toLocaleString()} hours
            </Badge>
          </div>
        </div>
      </div>

      {/* Charts */}
      <GridLayout
        autoSize={true}
        cols={18}
        rowHeight={1620 / 18}
        width={1620}
        compactType="horizontal"
        margin={[20, 20]}
        layout={updateLayoutWithStatic(layout)}
        onResize={(_, __, layoutItem, placeholder) => {
          if (layoutItem.w < 3) {
            layoutItem.w = 3;
            placeholder.w = 3;
          }
          if (layoutItem.w / layoutItem.h !== 2) {
            layoutItem.h = layoutItem.w / 2;
            placeholder.h = layoutItem.w / 2;
          }
        }}
      >
        {/* Meetings per day */}
        {activeCharts.has("meetings-per-day") && (
          <div key="meetings-per-day">
            <ChartTitle
              title="MEETINGS PER DAY"
              onDownload={() => {
                const csvContent = [
                  ["Date", "Count"],
                  ...(notesByDate.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    notesByDate.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meetings_per_day.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={notesByDate}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "top",
                    anchor: "end",
                  },
                },
                layout: makeLayout(),
                elements: makeBarElements(),
                scales: makeScales(),
              }}
            />
          </div>
        )}

        {/* Meetings using Zeplyn by advisor */}
        {activeCharts.has("meetings-using-zeplyn") && (
          <div key="meetings-using-zeplyn">
            <ChartTitle
              title="MEETINGS USING ZEPYLN BY ADVISOR"
              onDownload={() => {
                const csvContent = [
                  ["Date", "Advisor", "Count"],
                  ...(notesByOwner.labels ?? []).flatMap((label, index) =>
                    notesByOwner.datasets.map((dataset) => [
                      `"${label}"`,
                      `"${dataset.label}"`,
                      dataset.data[index],
                    ])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(
                  csvContent,
                  "meetings_using_zeplyn_by_advisor.csv"
                );
              }}
            />
            <Line
              className={"self-center"}
              data={notesByOwner}
              options={{
                plugins: {
                  legend: {
                    position: "bottom",
                    fullSize: false,
                    labels: {
                      boxWidth: 12,
                      padding: 8,
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                scales: makeScales({ showYGrid: true, showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Meeting method */}
        {activeCharts.has("meeting-method") && (
          <div key="meeting-method">
            <ChartTitle
              title="MEETING METHOD"
              onDownload={() => {
                const csvContent = [
                  ["Method", "Count"],
                  ...(notesByAudioSource.labels ?? []).map((label, index) => [
                    label,
                    notesByAudioSource.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meeting_method.csv");
              }}
            />
            <Pie
              data={notesByAudioSource}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Meeting outcomes */}
        {activeCharts.has("meeting-outcomes") && (
          <div key="meeting-outcomes">
            <ChartTitle
              title="MEETING OUTCOMES"
              onDownload={() => {
                const csvContent = [
                  ["Outcome", "Count"],
                  ...(notesByStatus.labels ?? []).map((label, index) => [
                    label,
                    notesByStatus.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meeting_outcomes.csv");
              }}
            />
            <Pie
              data={notesByStatus}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Tasks assigned */}
        {activeCharts.has("tasks-assigned") && (
          <div key="tasks-assigned">
            <ChartTitle
              title="TASKS ASSIGNED"
              onDownload={() => {
                const csvContent = [
                  ["Assignee", "Count"],
                  ...(tasksByAssignee.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    tasksByAssignee.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "tasks_assigned.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={tasksByAssignee}
              options={{
                indexAxis: "y",
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "right",
                    anchor: "end",
                  },
                },
                layout: makeLayout(40),
                elements: makeBarElements(),
                scales: makeScales({ showXTicks: false }),
              }}
            />
          </div>
        )}

        {/* Zeplyn usage (time) by advisor */}
        {activeCharts.has("usage-by-advisor") && (
          <div key="usage-by-advisor">
            <ChartTitle
              title="ZEPLYN MEETING TIME BY ADVISOR (HOURS)"
              onDownload={() => {
                const csvContent = [
                  ["Date", "Advisor", "Hours"],
                  ...(durationByAdvisorAndDay.labels ?? []).flatMap(
                    (label, index) =>
                      durationByAdvisorAndDay.datasets.map((dataset) => [
                        `"${label}"`,
                        `"${dataset.label}"`,
                        dataset.data[index],
                      ])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "usage_by_advisor.csv");
              }}
            />
            <Line
              className={"self-center"}
              data={durationByAdvisorAndDay}
              options={{
                plugins: {
                  legend: {
                    position: "bottom",
                    fullSize: false,
                    labels: {
                      boxWidth: 12,
                      padding: 8,
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                scales: makeScales({ showYGrid: true, showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Zeplyn usage (time) by client segment */}
        {activeCharts.has("usage-by-client-segment") && (
          <div key="usage-by-client-segment">
            <ChartTitle
              title="ZEPLYN MEETING TIME BY CLIENT SEGMENT (HOURS)"
              onDownload={() => {
                const csvContent = [
                  ["Date", "Client Segment", "Hours"],
                  ...(durationByClientSegment.labels ?? []).flatMap(
                    (label, index) =>
                      durationByClientSegment.datasets.map((dataset) => [
                        `"${label}"`,
                        `"${dataset.label}"`,
                        dataset.data[index],
                      ])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "usage_by_client_segment.csv");
              }}
            />
            <Line
              className={"self-center"}
              data={durationByClientSegment}
              options={{
                plugins: {
                  legend: {
                    position: "bottom",
                    fullSize: false,
                    labels: {
                      boxWidth: 12,
                      padding: 8,
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                scales: makeScales({ showYGrid: true, showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Zeplyn usage (average duration) by client segment */}
        {activeCharts.has("average-duration-by-client-segment") && (
          <div key="average-duration-by-client-segment">
            <ChartTitle
              title="AVERAGE MEETING DURATION BY CLIENT SEGMENT (MINUTES)"
              onDownload={() => {
                const csvContent = [
                  ["Client Segment", "Average Duration (minutes)"],
                  ...(averageDurationByClientSegment.labels ?? []).map(
                    (label, index) => [
                      label,
                      averageDurationByClientSegment.datasets[0]?.data[index],
                    ]
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(
                  csvContent,
                  "zeplyn_usage_by_client_segment.csv"
                );
              }}
            />
            <Bar
              className={"self-center"}
              data={averageDurationByClientSegment}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "top",
                    anchor: "end",
                  },
                },
                elements: makeBarElements(),
                layout: makeLayout(),
                scales: makeScales({ showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Meetings with Zeplyn vs scheduled meetings  */}
        {activeCharts.has("scheduled-events") && (
          <div key="scheduled-events">
            <ChartTitle
              title="CLIENT CALENDAR MEETINGS WITH ZEPLYN"
              onDownload={() => {
                const csvContent = [
                  ["Zeplyn meetings", "Calendar meetings"],
                  ...(notesAndScheduledEventCounts.labels ?? []).map(
                    (label, index) => [
                      label,
                      notesAndScheduledEventCounts.datasets[0]?.data[index],
                    ]
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "zeplyn_vs_calendar_meetings.csv");
              }}
            />
            <Pie
              data={notesAndScheduledEventCounts}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Client count */}
        {activeCharts.has("client-count") && (
          <div key="client-count">
            <ChartTitle
              title="NUMBER OF CLIENTS MET WITH"
              onDownload={() => {
                const csvContent = [
                  ["Advisor", "Client count"],
                  ...(clientCountByOwner.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    clientCountByOwner.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "client_count.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={clientCountByOwner}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "top",
                    anchor: "end",
                  },
                },
                elements: makeBarElements(),
                scales: makeScales({ showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Topics */}
        {activeCharts.has("topics") && (
          <div key="topics">
            <ChartTitle
              title="TOPICS"
              onDownload={() => {
                const csvContent = [
                  ["Topic", "Count", "Percentage"],
                  ...Object.entries(countsByTag).map(([tag, data]) => [
                    `"${tag}"`,
                    data.count,
                    data.percentage,
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "topics.csv");
              }}
            >
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => setShowTagPercentages(!showTagPercentages)}
              >
                {showTagPercentages ? "Show counts" : "Show percentages"}
              </Button>
            </ChartTitle>
            <div
              className={
                "m-1 mr-4 grid grid-flow-col grid-rows-6 gap-x-[1px] bg-border"
              }
            >
              {fillToMultipleOfLength(
                Object.entries(countsByTag)
                  .sort(
                    ([_, { count: countOne }], [__, { count: countTwo }]) =>
                      countTwo - countOne
                  )
                  .slice(0, 18),
                6,
                ["", { count: 0, percentage: "0" }]
              ).map(([tag, data], i) => (
                <div
                  key={data.count > 0 ? tag : i}
                  className={cn(
                    "flex flex-row items-center justify-between bg-background py-1",
                    i < 6 ? "pr-6" : i < 12 ? "pl-6 pr-6" : "pl-6"
                  )}
                >
                  {data.count > 0 && (
                    <>
                      <Typography
                        variant="default"
                        className="text-sky-700"
                        asChild
                      >
                        <Link
                          className="line-clamp-1 underline"
                          to={`/notes?searchTerm=${encodeURIComponent(tag)}`}
                        >
                          {tag}
                        </Link>
                      </Typography>
                      <Typography variant="default" asChild>
                        <span className="ml-8">
                          {showTagPercentages
                            ? `${data.percentage}%`
                            : data.count}
                        </span>
                      </Typography>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Client Topics */}
        {activeCharts.has("client-topics") && (
          <div key="client-topics">
            <ChartTitle
              title="CLIENT TOPICS"
              onDownload={() => {
                const csvContent = [
                  ["Client", "Topic (in order of frequency)"],
                  ...Object.entries(tagsByClient).flatMap(
                    ([_, { name, tags }]) =>
                      tags.map((tag) => [`"${name}"`, `"${tag}"`])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "client_topics.csv");
              }}
            />
            <div className={"grid-col m-1 grid grid-cols-5 gap-x-8"}>
              {Object.entries(tagsByClient).filter(
                ([_, { tags }]) => tags.length > 0
              ).length === 0 && (
                <Typography variant="default" className="col-span-5">
                  No tags available for any clients.
                </Typography>
              )}

              {Object.entries(tagsByClient)
                .sort(
                  ([_, clientTagsOne], [__, clientTagsTwo]) =>
                    clientTagsTwo.tags.length - clientTagsOne.tags.length
                )
                .filter(([_, { tags }]) => tags.length > 0)
                .slice(0, 5)
                .map(([uuid, { name, tags }]) => (
                  <div
                    key={uuid}
                    className="flex flex-col items-center gap-y-2"
                  >
                    <Typography variant="default" asChild>
                      <Link
                        className="pb-2 text-sky-700 underline"
                        to={`/clients/${uuid}`}
                      >
                        {name}
                      </Link>
                    </Typography>
                    {tags.slice(0, 3).map((tag) => (
                      <Badge
                        key={`${uuid}-${tag}`}
                        variant="slate"
                        className="line-clamp-3 w-full justify-center text-center"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Meetings by client segment */}
        {activeCharts.has("notes-by-client-segment") && (
          <div key="notes-by-client-segment">
            <ChartTitle
              title="MEETINGS PER CLIENT SEGMENT"
              onDownload={() => {
                const csvContent = [
                  ["Client segment", "Count"],
                  ...(notesByClientSegment.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    notesByClientSegment.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meetings_by_client_segment.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={notesByClientSegment}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "top",
                    anchor: "end",
                  },
                },
                layout: makeLayout(),
                elements: makeBarElements(),
                scales: makeScales(),
              }}
            />
          </div>
        )}

        {/* Meetings by whether prep was generated */}
        {activeCharts.has("meeting-prep-generated") && (
          <div key="meeting-prep-generated">
            <ChartTitle
              title="MEETINGS WITH MEETING PREP"
              onDownload={() => {
                const csvContent = [
                  ["Prep generated", "Count"],
                  ...(notesByPrepGenerated.labels ?? []).map((label, index) => [
                    label,
                    notesByPrepGenerated.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meetings_with_prep_generated.csv");
              }}
            />
            <Pie
              data={notesByPrepGenerated}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Clients by life phase */}
        {activeCharts.has("clients-by-life-phase") && (
          <div key="clients-by-life-phase">
            <ChartTitle
              title="CLIENTS BY LIFE PHASE"
              onDownload={() => {
                const csvContent = [
                  ["Life phase", "Count"],
                  ...(clientsByLifePhase.labels ?? []).map((label, index) => [
                    label,
                    clientsByLifePhase.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "clients_by_life_phase.csv");
              }}
            />
            <Pie
              data={clientsByLifePhase}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Meetings by client by year */}
        {activeCharts.has("meetings-by-client-by-year") && (
          <div key="meetings-by-client-by-year">
            <ChartTitle
              title="MEETINGS BY CLIENT BY YEAR"
              onDownload={() => {
                const csvContent = [
                  ["Client", "Year", "Count"],
                  ...Object.entries(meetingsByClientByYear.data).flatMap(
                    ([_, clientData]) =>
                      meetingsByClientByYear.years.map((year) => [
                        clientData.id,
                        year,
                        clientData.countsByYear[year] ?? 0,
                      ])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meetings_by_client_by_year.csv");
              }}
            />
            <DataTable
              columns={
                [
                  {
                    name: "Client",
                    selector: (row) => row.name,
                    sortable: true,
                    grow: 3,
                    wrap: true,
                  },
                  ...(meetingsByClientByYear.years.map((year) => ({
                    name: year,
                    selector: (row) => row.countsByYear[year] ?? 0,
                    sortable: true,
                  })) as TableColumn<MeetingsByClientByYearRowType>[]),
                ] as TableColumn<MeetingsByClientByYearRowType>[]
              }
              data={Object.entries(meetingsByClientByYear.data).map(
                ([_, value]) => value
              )}
              pagination
              dense
            />
          </div>
        )}
      </GridLayout>
    </div>
  );
};

// Tabs that can be selected on the dashboard.
type Tab = "client" | "practice" | "clientGroupings";

const tabs = [
  {
    label: "Client metrics",
    value: "client",
  },
  {
    label: "Practice metrics",
    value: "practice",
  },
  {
    label: "Aggregate client metrics",
    value: "clientGroupings",
  },
];

const TabbedCharts = ({
  insights,
}: {
  insights: InsightsDashboardResponse;
}) => {
  const [selectedTab, setSelectedTab] = useState<Tab>("client");

  return (
    <div className="w-full">
      <Tabs
        value={selectedTab}
        onValueChange={(newValue) => {
          setSelectedTab(newValue as Tab);
        }}
        className="self-start"
      >
        <TabsList>
          {tabs.map(({ label, value }) => (
            <TabsTrigger key={value} value={value}>
              {label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
      <Charts insights={insights} context={selectedTab} />
    </div>
  );
};

// Displays a grid of charts and tables with information about the user's (and/or user's
// organization's, depending on the user's privilege level) Zeplyn usage.
const InsightsDashboard = () => {
  const { insightsPromise } = useLoaderData<typeof loader>();

  const Processing = () => (
    <Typography variant="h4">
      Processing dashboard data. Please refresh in a few seconds.
    </Typography>
  );

  return (
    <LayoutV2>
      <ContentV2>
        <Suspense fallback={<Skeletons />}>
          <Await resolve={insightsPromise} errorElement={<Processing />}>
            {(insights) =>
              insights === undefined || insights.status === Status.Pending ? (
                <Processing />
              ) : (
                <ClientOnly>
                  {() => <TabbedCharts insights={insights} />}
                </ClientOnly>
              )
            }
          </Await>
        </Suspense>
      </ContentV2>
    </LayoutV2>
  );
};

export default InsightsDashboard;
