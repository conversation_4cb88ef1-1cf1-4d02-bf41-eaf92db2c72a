// API URL: /feapi/tasks/edit
import { ActionFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi, TaskUpdate } from "~/api/openapi/generated";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const { uuid, parentNoteUuid, assignee, completed, title, dueDate } = body;

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const taskApi = new TaskApi(configuration);

    const taskUpdate: TaskUpdate = {
      parentNoteUuid,
    };
    assignee && (taskUpdate.assignee = assignee);
    typeof completed === "boolean" && (taskUpdate.completed = completed);
    title && (taskUpdate.title = title);
    dueDate && (taskUpdate.dueDate = new Date(dueDate));

    const result = await taskApi.taskEditTask({
      taskUuid: uuid,
      taskUpdate,
    });

    return { success: true };
  } catch (e) {
    return { success: false };
  }
};
