// @vitest-environment node

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { action } from "./route";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { v4 as uuidv4 } from "uuid";

// Mock dependencies
vi.mock("~/api/openapi/configParams");
vi.mock("~/api/openapi/generated");

describe("action", () => {
  const mockConfigParams = {
    basePath: "http://localhost:8000",
    middleware: [],
  };

  const mockTaskEditTask = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock configurationParameters
    vi.mocked(configurationParameters).mockResolvedValue(mockConfigParams);

    // Mock TaskApi
    mockTaskEditTask.mockResolvedValue({});
    vi.mocked(TaskApi).mockImplementation(
      () =>
        ({
          taskEditTask: mockTaskEditTask,
        }) as any
    );

    // Mock Configuration
    vi.mocked(Configuration).mockImplementation((params) => params as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should edit a task successfully with all fields", async () => {
    const taskUuid = uuidv4();
    const parentNoteUuid = uuidv4();
    const assignee = uuidv4();
    const dueDate = "2024-12-31T23:59:59.000Z";

    const requestBody = {
      uuid: taskUuid,
      parentNoteUuid,
      assignee,
      completed: true,
      title: "Updated Task Title",
      dueDate,
    };

    const request = new Request("http://localhost:3000/feapi/tasks/edit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: true,
    });

    expect(configurationParameters).toHaveBeenCalledWith(request);
    expect(Configuration).toHaveBeenCalledWith(mockConfigParams);
    expect(TaskApi).toHaveBeenCalledWith(mockConfigParams);
    expect(mockTaskEditTask).toHaveBeenCalledWith({
      taskUuid,
      taskUpdate: {
        parentNoteUuid,
        assignee,
        completed: true,
        title: "Updated Task Title",
        dueDate: new Date(dueDate),
      },
    });
  });

  it("should edit a task with only required fields", async () => {
    const taskUuid = uuidv4();
    const parentNoteUuid = uuidv4();

    const requestBody = {
      uuid: taskUuid,
      parentNoteUuid,
    };

    const request = new Request("http://localhost:3000/feapi/tasks/edit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: true,
    });

    expect(mockTaskEditTask).toHaveBeenCalledWith({
      taskUuid,
      taskUpdate: {
        parentNoteUuid,
      },
    });
  });

  it("should ignore falsy values for optional fields", async () => {
    const taskUuid = uuidv4();
    const parentNoteUuid = uuidv4();

    const requestBody = {
      uuid: taskUuid,
      parentNoteUuid,
      assignee: "", // falsy value should be ignored
      title: "", // falsy value should be ignored
      completed: null, // not a boolean, should be ignored
      dueDate: null, // falsy value should be ignored
    };

    const request = new Request("http://localhost:3000/feapi/tasks/edit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: true,
    });

    expect(mockTaskEditTask).toHaveBeenCalledWith({
      taskUuid,
      taskUpdate: {
        parentNoteUuid,
      },
    });
  });

  it("should return error when configurationParameters throws", async () => {
    vi.mocked(configurationParameters).mockRejectedValue(
      new Error("Configuration failed")
    );

    const requestBody = {
      uuid: uuidv4(),
      parentNoteUuid: uuidv4(),
    };

    const request = new Request("http://localhost:3000/feapi/tasks/edit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskEditTask).not.toHaveBeenCalled();
  });

  it("should return error when TaskApi.taskEditTask throws", async () => {
    mockTaskEditTask.mockRejectedValue(new Error("API call failed"));

    const requestBody = {
      uuid: uuidv4(),
      parentNoteUuid: uuidv4(),
    };

    const request = new Request("http://localhost:3000/feapi/tasks/edit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(mockTaskEditTask).toHaveBeenCalled();
  });

  it("should return error when request body is invalid JSON", async () => {
    const request = new Request("http://localhost:3000/feapi/tasks/edit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: "invalid json",
    });

    const result = await action({ request, params: {}, context: {} });

    expect(result).toEqual({
      success: false,
    });

    expect(configurationParameters).not.toHaveBeenCalled();
    expect(mockTaskEditTask).not.toHaveBeenCalled();
  });
});
