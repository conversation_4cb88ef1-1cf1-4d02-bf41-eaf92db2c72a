import { NotebookText } from "lucide-react";
import { SerializeFrom } from "~/types/remix";

import { FormField, FormControl, FormLabel } from "~/@shadcn/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { MeetingType } from "~/api/openapi/generated";

export const MeetingTypeSelector = ({
  meetingTypes,
  meetingType,
  setMeetingType,
  label = "Meeting Type",
}: {
  meetingTypes: SerializeFrom<MeetingType>[];
  meetingType: SerializeFrom<MeetingType>;
  setMeetingType: (meetingType: SerializeFrom<MeetingType>) => void;
  label?: string;
}) => (
  <FormField id="meetingType" name="meetingType" className="w-fit" required>
    <FormLabel>{label}</FormLabel>
    <Select
      name="meetingType"
      onValueChange={(nextValue) => {
        const meetingType = meetingTypes.find((t) => t.uuid === nextValue);
        if (!meetingType) {
          return;
        }
        setMeetingType(meetingType);
      }}
      value={meetingType.uuid}
    >
      <FormControl>
        <SelectTrigger leftIcon={<NotebookText />}>
          <SelectValue />
        </SelectTrigger>
      </FormControl>
      <SelectContent>
        {meetingTypes.map((t) => (
          <SelectItem key={t.uuid} value={t.uuid}>
            {t.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  </FormField>
);
