import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Spark<PERSON> } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { MeetingTypeSelector } from "~/routes/_auth.notes.create.($id)/components/MeetingTypeSelector";
import { ClientInteraction, MeetingType } from "~/api/openapi/generated";
import { Button } from "~/@shadcn/ui/button";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { AttendeeOption, AttendeeOptions } from "~/api/attendees/types";
import { VirtualizedMultiSelect } from "~/@ui/VirtualizedMultiSelect";

interface MeetingPrepModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (meetingType: MeetingType) => void;
  meetingTypes: MeetingType[];
  meetingType: MeetingType;
  setMeetingType: (meetingType: MeetingType) => void;
  isLoading: boolean;

  interaction?: ClientInteraction;
  isMeetingPrepRevampEnabled: boolean;
  selectedAttendees: AttendeeOption[];
  allClients: AttendeeOptions;
  crmEntities: AttendeeOptions;
  setCrmEntities: Dispatch<SetStateAction<AttendeeOptions>>;
}

const MeetingPrepModal: React.FC<MeetingPrepModalProps> = ({
  isOpen,
  onClose,
  onSave,
  meetingTypes,
  meetingType,
  setMeetingType,
  isLoading,

  interaction,
  isMeetingPrepRevampEnabled,
  selectedAttendees,
  allClients,
  crmEntities,
  setCrmEntities,
}) => {
  const [isCrmEntitiesTouched, setCrmEntitiesTouched] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setCrmEntitiesTouched(false);
      setCrmEntities([]);
    }

    // on modal open: if `isCrmEntitiesTouched` is false, populate `crmEntities` with "allClients" from `selectedAttendees`
    if (isOpen && !isCrmEntitiesTouched) {
      setCrmEntities(
        selectedAttendees.filter((attendee) => attendee.type === "client")
      );
    }
  }, [isOpen, isCrmEntitiesTouched, setCrmEntities, selectedAttendees]);

  let ctaText = "Generate Meeting Prep";
  if (isLoading) {
    ctaText = "Processing...";
  } else if (interaction) {
    ctaText = "Regenerate Meeting Prep";
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate Meeting Prep</DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <MeetingTypeSelector
          meetingType={meetingType}
          setMeetingType={setMeetingType}
          meetingTypes={meetingTypes}
        />

        {isMeetingPrepRevampEnabled && (
          <FormField name="crmEntities" className="w-fit">
            <FormLabel>CRM Records</FormLabel>

            <VirtualizedMultiSelect
              options={allClients.map((c) => ({
                label:
                  "clientType" in c && c.clientType && c.clientType !== ""
                    ? `${c.name} (${c.clientType})`
                    : c.name,
                value: c.uuid,
              }))}
              selected={crmEntities.map((c) => c.uuid)}
              onChange={(entities) => {
                setCrmEntitiesTouched(true);

                const entityList =
                  typeof entities === "function"
                    ? entities(allClients.map((c) => c.uuid))
                    : entities;

                const selectedClients = allClients.filter((c) =>
                  entityList.includes(c.uuid)
                );
                setCrmEntities(selectedClients);
              }}
              placeholder="Select CRM Records"
              triggerClassName="rounded-2xl border-border p-3 shadow-sm hover:border-foreground hover:bg-accent"
              maxHeightPx={250}
            />
          </FormField>
        )}

        <DialogFooter>
          <Button onClick={onClose} variant="ghost" disabled={isLoading}>
            Cancel
          </Button>

          <Button onClick={() => onSave(meetingType)} disabled={isLoading}>
            <Sparkles />
            {ctaText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MeetingPrepModal;
