// API URL: /feapi/insights/rawdata
import { LoaderFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, InsightsApi } from "~/api/openapi/generated";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const configuration = new Configuration(
    await configurationParameters(request)
  );
  const insightsApi = new InsightsApi(configuration);

  // This API endpoint returns raw ZIP data, with proper headers to trigger a file download, so we
  // return the raw result rather than using the generated API machinery to try to convert it into
  // JSON or text (which we do not want here).
  return (await insightsApi.insightsGetRawInsightsDataRaw()).raw;
};
