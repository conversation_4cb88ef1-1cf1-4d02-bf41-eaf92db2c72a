import { LoaderFunctionArgs, redirect } from "react-router";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import getCookieValue from "~/utils/getCookieValue";
import { logError } from "~/utils/log.server";
import providers from "./providers";
import { OAuthRequestCrmTypeEnum } from "~/api/openapi/generated";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  const authorizationCode = searchParams.get("code");
  if (!authorizationCode) {
    throw new Error("Missing authorization_code query parameter");
  }

  const { id } = params;
  const state = searchParams.get("state");

  const provider = providers[id || ""]?.[state || "default"];
  if (!provider) {
    throw new Error(`No providers found for ${id} & ${state}`);
  }

  const { name, providerEnum, codeVerifierCookieName } = provider;

  // fetch code_verifier from cookie - if available
  const codeVerifier = codeVerifierCookieName
    ? getCookieValue(
        request.headers.get("Cookie") ?? "",
        codeVerifierCookieName
      )
    : undefined;

  try {
    await setUpOAuthProvider({
      authorizationCode,
      provider: providerEnum,
      request,
      codeVerifier,
      crmType: state as OAuthRequestCrmTypeEnum,
    });
    return redirect(
      `/settings/integrations?type=integration&status=true&name=${name}`
    );
  } catch (error) {
    logError(`Failed to setup ${name} integration`, error);
    return redirect(
      `/settings/integrations?type=integration&status=false&name=${name}`
    );
  }
};
