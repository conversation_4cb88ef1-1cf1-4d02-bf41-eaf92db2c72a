import { OAuthRequestProviderEnum } from "~/api/openapi/generated";

type Provider = {
  name: string;
  providerEnum: OAuthRequestProviderEnum;
  codeVerifierCookieName?: string; // NOTE: this value is `code_verifier_{{SectionItemIntegrationCard.id}}`
};

const providers: Record<string, Record<string, Provider>> = {
  advisorengine: {
    default: {
      name: "Advisor Engine",
      providerEnum: OAuthRequestProviderEnum.AdvisorEngine,
      codeVerifierCookieName: "code_verifier_connect_advisorengine",
    },
  },

  microsoft_dynamics: {
    base: {
      name: "Microsoft Dynamics",
      providerEnum: OAuthRequestProviderEnum.MicrosoftDynamics,
    },
    amplify: {
      name: "Amplify",
      providerEnum: OAuthRequestProviderEnum.MicrosoftDynamics,
    },
    salentica_engage: {
      name: "Salentica Engage",
      providerEnum: OAuthRequestProviderEnum.MicrosoftDynamics,
    },
    tamarac: {
      name: "Tamara<PERSON>",
      providerEnum: OAuthRequestProviderEnum.MicrosoftDynamics,
    },
  },

  salesforce: {
    default: {
      name: "Salesforce",
      providerEnum: OAuthRequestProviderEnum.Salesforce,
    },
    black_diamond: {
      name: "Black Diamond",
      providerEnum: OAuthRequestProviderEnum.Salesforce,
    },
    practifi: {
      name: "Practifi",
      providerEnum: OAuthRequestProviderEnum.Salesforce,
    },
    salentica_elements: {
      name: "Salentica Elements",
      providerEnum: OAuthRequestProviderEnum.Salesforce,
    },
    xlr8: {
      name: "XLR8",
      providerEnum: OAuthRequestProviderEnum.Salesforce,
    },
  },

  wealthbox: {
    default: {
      name: "Wealthbox",
      providerEnum: OAuthRequestProviderEnum.Wealthbox,
    },
  },

  hubspot: {
    default: {
      name: "HubSpot",
      providerEnum: OAuthRequestProviderEnum.Hubspot,
    },
  },
};

export default providers;
