import { useNavigate, Outlet, useLoaderData } from "react-router";
import { useEffect, useState } from "react";
import { fetchFlags } from "~/api/flags/flagFetcher.server";
import { type LoaderFunctionArgs } from "react-router";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const flags = await fetchFlags(request);
  return { flags };
};

const Route = () => {
  const { flags } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [redirectTo, setRedirectTo] = useState<string | null>(null);
  const [isNavigating, setIsNavigating] = useState(true);

  // First useEffect to get the redirectTo from localStorage
  useEffect(() => {
    const storedRedirect = localStorage.getItem("redirectTo");
    if (storedRedirect) {
      setRedirectTo(storedRedirect);
    } else {
      navigate("/dashboard", {
        replace: true,
      });
      setIsNavigating(false);
    }
  }, [navigate]);

  // Second useEffect to handle navigation if redirectTo is found
  useEffect(() => {
    if (redirectTo) {
      localStorage.removeItem("redirectTo");
      requestAnimationFrame(() => {
        navigate(redirectTo, { replace: true });
        setIsNavigating(false); // End the navigation block
      });
    }
  }, [redirectTo, navigate]);

  // Prevent rendering of initial screen while navigating
  if (isNavigating) {
    return null;
  }

  return <Outlet />;
};

export default Route;
