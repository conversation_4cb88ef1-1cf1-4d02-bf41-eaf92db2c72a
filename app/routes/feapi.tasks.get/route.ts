// API URL: /feapi/tasks/get
import { LoaderFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { logError } from "~/utils/log.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  let taskId;

  try {
    const url = new URL(request.url);
    taskId = url.searchParams.get("id");

    if (!taskId) {
      throw new Error("Missing taskId");
    }

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const taskApi = new TaskApi(configuration);

    return await taskApi.taskViewTask({
      taskUuid: taskId,
    });
  } catch (e) {
    logError(`Failed to fetch task (ID: ${taskId}).`, e);
    return null;
  }
};
