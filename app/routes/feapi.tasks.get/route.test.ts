// @vitest-environment node

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { loader } from "./route";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { logError } from "~/utils/log.server";
import { v4 as uuidv4 } from "uuid";

// Mock dependencies
vi.mock("~/api/openapi/configParams");
vi.mock("~/api/openapi/generated");
vi.mock("~/utils/log.server");

describe("loader", () => {
  const mockConfigParams = {
    basePath: "http://localhost:8000",
    middleware: [],
  };

  const mockTaskViewTask = vi.fn();
  const mockTaskResponse = {
    uuid: uuidv4(),
    title: "Test Task",
    description: "Test Description",
    dueDate: new Date("2024-12-31T23:59:59.000Z"),
    created: new Date("2024-01-01T00:00:00.000Z"),
    modified: new Date("2024-01-02T00:00:00.000Z"),
    completed: false,
    parentNoteUuid: uuidv4(),
    owner: null,
    assignee: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock configurationParameters
    vi.mocked(configurationParameters).mockResolvedValue(mockConfigParams);

    // Mock TaskApi
    mockTaskViewTask.mockResolvedValue(mockTaskResponse);
    vi.mocked(TaskApi).mockImplementation(
      () =>
        ({
          taskViewTask: mockTaskViewTask,
        }) as any
    );

    // Mock Configuration
    vi.mocked(Configuration).mockImplementation((params) => params as any);

    // Mock logError
    vi.mocked(logError).mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should fetch a task successfully", async () => {
    const taskId = uuidv4();
    const request = new Request(
      `http://localhost:3000/feapi/tasks/get?id=${taskId}`
    );

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toEqual(mockTaskResponse);

    expect(configurationParameters).toHaveBeenCalledWith(request);
    expect(Configuration).toHaveBeenCalledWith(mockConfigParams);
    expect(TaskApi).toHaveBeenCalledWith(mockConfigParams);
    expect(mockTaskViewTask).toHaveBeenCalledWith({
      taskUuid: taskId,
    });
    expect(logError).not.toHaveBeenCalled();
  });

  it("should return null when taskId is missing", async () => {
    const request = new Request("http://localhost:3000/feapi/tasks/get");

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toBeNull();

    expect(mockTaskViewTask).not.toHaveBeenCalled();
    expect(logError).toHaveBeenCalledWith(
      "Failed to fetch task (ID: null).",
      expect.any(Error)
    );
  });

  it("should return null when taskId is empty string", async () => {
    const request = new Request("http://localhost:3000/feapi/tasks/get?id=");

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toBeNull();

    expect(mockTaskViewTask).not.toHaveBeenCalled();
    expect(logError).toHaveBeenCalledWith(
      "Failed to fetch task (ID: ).",
      expect.any(Error)
    );
  });

  it("should return null when configurationParameters throws", async () => {
    const taskId = uuidv4();
    const request = new Request(
      `http://localhost:3000/feapi/tasks/get?id=${taskId}`
    );

    vi.mocked(configurationParameters).mockRejectedValue(
      new Error("Configuration failed")
    );

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toBeNull();

    expect(mockTaskViewTask).not.toHaveBeenCalled();
    expect(logError).toHaveBeenCalledWith(
      `Failed to fetch task (ID: ${taskId}).`,
      expect.any(Error)
    );
  });

  it("should return null when TaskApi.taskViewTask throws", async () => {
    const taskId = uuidv4();
    const request = new Request(
      `http://localhost:3000/feapi/tasks/get?id=${taskId}`
    );

    mockTaskViewTask.mockRejectedValue(new Error("API call failed"));

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toBeNull();

    expect(mockTaskViewTask).toHaveBeenCalledWith({
      taskUuid: taskId,
    });
    expect(logError).toHaveBeenCalledWith(
      `Failed to fetch task (ID: ${taskId}).`,
      expect.any(Error)
    );
  });

  it("should handle multiple query parameters correctly", async () => {
    const taskId = uuidv4();
    const request = new Request(
      `http://localhost:3000/feapi/tasks/get?id=${taskId}&other=value`
    );

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toEqual(mockTaskResponse);

    expect(mockTaskViewTask).toHaveBeenCalledWith({
      taskUuid: taskId,
    });
  });

  it("should handle URL with fragment and hash correctly", async () => {
    const taskId = uuidv4();
    const request = new Request(
      `http://localhost:3000/feapi/tasks/get?id=${taskId}#fragment`
    );

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toEqual(mockTaskResponse);

    expect(mockTaskViewTask).toHaveBeenCalledWith({
      taskUuid: taskId,
    });
  });

  it("should log error with correct taskId when error occurs", async () => {
    const taskId = "invalid-uuid";
    const request = new Request(
      `http://localhost:3000/feapi/tasks/get?id=${taskId}`
    );

    const apiError = new Error("Invalid UUID format");
    mockTaskViewTask.mockRejectedValue(apiError);

    const result = await loader({ request, params: {}, context: {} });

    expect(result).toBeNull();
    expect(logError).toHaveBeenCalledWith(
      `Failed to fetch task (ID: ${taskId}).`,
      apiError
    );
  });
});
