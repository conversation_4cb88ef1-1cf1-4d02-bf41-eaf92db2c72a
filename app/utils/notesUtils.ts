import { v4 as uuidv4 } from "uuid";
import { isSameDay } from "date-fns";
import {
  ClientInteraction,
  ListNotesResponse,
  ProcessingStatus,
  ScheduledEvent,
} from "~/api/openapi/generated";
import safeBtoa from "~/utils/safeBtoa";

export interface Attendee {
  uuid: string;
  name: string;
  type?: string;
}

interface CombinedEventBase {
  id: string;
  interaction?: ClientInteraction;
  meetingName: string;
  created: string;
  startTime: string;
  endTime?: string;
  attendees?: Attendee[];
  meetingLink?: string;
  status?: ProcessingStatus;
  tags?: string[];
  meetingType?: string | null;
  toMeeting: {
    pathname: string;
    search?: string;
  };
  toPrep: {
    pathname: string;
    search?: string;
  };
  autojoinEnabled?: boolean;
  autojoinAvailable?: boolean;
  scheduledEventUUID?: string;
}

interface CalendarCombinedEvent extends CombinedEventBase {
  type: "calendarEvent";
}

interface NoteCombinedEvent extends CombinedEventBase {
  type: "note";
}

export type CombinedEvent = CalendarCombinedEvent | NoteCombinedEvent;

// Parses attendees from a calendar event
export const parseAttendees = (
  participants: Array<{
    zeplynUuid?: string | null;
    name?: string | null;
    emailAddress?: string | null;
    zeplynKind?: string | null;
  }>
): Attendee[] =>
  participants.map((participant) => ({
    uuid: participant.zeplynUuid ?? uuidv4(),
    name: participant.name || participant.emailAddress || "Unknown",
    type: participant.zeplynKind ?? "unknown",
  }));

// Create search params for a calendar event or scheduled note route.
export const searchParamsForCreate = ({
  event,
  note,
  forPrep,
}: {
  event?: ScheduledEvent;
  note?: ListNotesResponse;
  forPrep: boolean;
}) => {
  const params = new URLSearchParams();
  if (event) {
    params.append("meetingTitle", event.title);
    if (event.meetingUrls[0]) {
      params.append("meetingLink", event.meetingUrls[0]);
    }
    params.append("startTime", event.startTime.toISOString());
    params.append("endTime", event.endTime.toISOString());
    if ("scheduledEventUuid" in event) {
      params.append("scheduledEventUUID", event.scheduledEventUuid);
    }
    if (
      event.linkedCrmEntity &&
      event.linkedCrmEntity.id &&
      event.linkedCrmEntity.name
    ) {
      params.append("linkedCRMEntityID", event.linkedCrmEntity.id);
      params.append("linkedCRMEntityName", event.linkedCrmEntity.name);
    }

    const attendees = parseAttendees(event.participants);
    params.append("attendees", safeBtoa(JSON.stringify(attendees)));
  }
  if (note) {
    params.append("noteID", note.uuid);
  }
  if (forPrep) {
    // TODO: @debojyotighosh Remove `tab=prep` query param once `EnableNewMeetingPrepFlow` flag is enabled for all users
    params.append("tab", "prep");
    params.append("action", "meeting-prep");
  }
  return params.toString();
};

// Get a localized all-day event time for calendar events
export const getAllDayEventTime = (dateString: string) => {
  const allDayDate = new Date(dateString);
  return new Date(
    allDayDate.getUTCFullYear(),
    allDayDate.getUTCMonth(),
    allDayDate.getUTCDate()
  ).toISOString();
};

/**
 * Combines calendar events and upcoming notes into a single sorted array of CombinedEvents.
 *
 * @param calendarEvents - Array of calendar events from the backend.
 * @param upcomingNotes - Array of upcoming notes.
 * @param clientInteractions - Array of client interactions.
 * @param meetingSourceIDs - A set of meetingSourceIds to exclude (notes already existing for these events).
 * @param filterToday - If true, filters to events occurring today. If false, filters out today's events. If undefined, no filter is applied.
 * @param searchTerm - Optional search term to filter notes by - via URL.
 * @param appliedSearchQuery - Optional search term to filter notes by - via search bar.
 *
 * @returns A sorted array of CombinedEvent objects.
 */
export const combineEvents = (
  calendarEvents: ScheduledEvent[],
  upcomingNotes: ListNotesResponse[],
  clientInteractions: ClientInteraction[],
  meetingSourceIDs: Set<string>,
  filterToday?: boolean,
  searchTerm?: string,
  appliedSearchQuery?: string
): CombinedEvent[] => {
  const now = new Date();

  // Map note UUIDs to their respective interactions.
  const interactionIdToInteraction = new Map(
    clientInteractions.map((interaction) => [interaction.noteUuid, interaction])
  );

  const filteredCalendarEvents = calendarEvents
    .filter((event) => !meetingSourceIDs.has(event.id))
    .filter((event) => {
      return (
        !appliedSearchQuery ||
        event.title.toLowerCase().includes(appliedSearchQuery.toLowerCase()) ||
        event.participants.some(
          (participant) =>
            (participant.emailAddress &&
              participant.emailAddress
                .toLowerCase()
                .includes(appliedSearchQuery.toLowerCase())) ||
            (participant.name &&
              participant.name
                .toLowerCase()
                .includes(appliedSearchQuery.toLowerCase()))
        )
      );
    })
    .filter((event) => {
      return (
        !searchTerm ||
        event.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    })
    .filter((event) => {
      if (filterToday === undefined) return true;
      const eventIsToday = isSameDay(new Date(event.startTime), now);
      return filterToday === eventIsToday;
    })
    .map<CalendarCombinedEvent>((event) => {
      return {
        type: "calendarEvent",
        id: event.id,
        meetingName: event.title.length > 0 ? event.title : "<No title>",
        meetingLink: event.meetingUrls[0],
        interaction: undefined,
        startTime: event.allDay
          ? getAllDayEventTime(
              typeof event.startTime === "string"
                ? event.startTime
                : event.startTime.toISOString()
            )
          : typeof event.startTime === "string"
            ? event.startTime
            : event.startTime.toISOString(),
        endTime:
          typeof event.endTime === "string"
            ? event.endTime
            : event.endTime.toISOString(),
        created: new Date().toISOString(),
        attendees: parseAttendees(event.participants),
        // No tags or status for calendar events by default
        toPrep: {
          pathname: `/notes/create/${event.id}`,
          search: searchParamsForCreate({
            event,
            forPrep: true,
          }),
        },
        toMeeting: {
          pathname: `/notes/create/${event.id}`,
          search: searchParamsForCreate({
            event,
            forPrep: false,
          }),
        },
        autojoinEnabled: event.autojoinEnabled,
        autojoinAvailable: event.autojoinAvailable,
        scheduledEventUUID: event.scheduledEventUuid,
      };
    });

  const mappedNotes = upcomingNotes
    .filter((note) => {
      if (filterToday === undefined) return true;
      // If the note's status is not scheduled, assume it's not a meeting for today. This should not
      // happen, because these are "upcoming" notes, which should always be in the "scheduled" state
      // until processing begins, but that's not enforced by the type system, so we need to handle
      // it here. This is a balance between trying to prevent irrelevant meetings from showing up as
      // active on the "today" tab of the dashboard (or in the scheduled notes list), and not having
      // relevant meetings show up in the "today" tab when they are active (e.g., a bot meeting that
      // was just created manually).
      const eventIsToday =
        note.status === ProcessingStatus.Scheduled &&
        isSameDay(new Date(note.scheduledStartTime ?? note.created), now);
      return filterToday === eventIsToday;
    })
    .map<NoteCombinedEvent>((note) => {
      const toMeeting =
        note.status === ProcessingStatus.Scheduled
          ? {
              pathname: `/notes/create/${note.uuid}`,
              search: searchParamsForCreate({
                note,
                forPrep: false,
              }),
            }
          : {
              pathname: `/notes/${note.uuid}`,
              search: searchTerm ? `?searchTerm=${searchTerm}` : undefined,
            };
      const toPrep = {
        pathname: `/notes/create/${note.uuid}`,
        search: searchParamsForCreate({ note, forPrep: true }),
      };
      const relatedCalendarEvent = calendarEvents.find(
        (e) => e.id === note.meetingSourceId
      );
      return {
        type: "note",
        id: note.uuid,
        meetingName: note.meetingName,
        meetingLink: relatedCalendarEvent?.meetingUrls[0],
        created: note.created,
        startTime: note.scheduledStartTime || note.created,
        // TODO: remove the logic for calculating an end time.
        endTime:
          note.scheduledEndTime ||
          new Date(
            new Date(note.scheduledStartTime || note.created).getTime() +
              60 * 60 * 1000
          ).toISOString(),
        interaction: interactionIdToInteraction.get(note.uuid),
        attendees: relatedCalendarEvent
          ? parseAttendees(relatedCalendarEvent.participants)
          : note.attendees,
        status: note.status as ProcessingStatus,
        tags: note.tags,
        meetingType: note.meetingType ?? null,
        toMeeting: toMeeting,
        toPrep: toPrep,
        autojoinEnabled: relatedCalendarEvent?.autojoinEnabled ?? false,
        autojoinAvailable: relatedCalendarEvent?.autojoinAvailable ?? false,
        scheduledEventUUID: relatedCalendarEvent?.scheduledEventUuid,
      };
    });

  const combinedEvents: CombinedEvent[] = [
    ...filteredCalendarEvents,
    ...mappedNotes,
  ];

  return combinedEvents.sort(
    (a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
  );
};

export const isNoteEmpty = (note: ListNotesResponse): boolean => {
  // Check for the "Empty note" tag
  if (note.tags?.includes("Empty note")) return true;
  return false;
};
