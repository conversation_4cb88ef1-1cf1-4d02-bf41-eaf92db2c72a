import { toast } from "react-toastify";

import stripHtml from "./stripHtml";
import { marked } from "marked";

export const copyToClipboard = (text: string, section?: string) => {
  navigator.clipboard.writeText(text).then(
    () => {
      if (section) toast.success(`Copied ${section} to clipboard!`);
      else toast.success("Copied to clipboard!");
    },
    (err) => {
      toast.error("Failed to copy text.");
    }
  );
};

export type CopyableSection = {
  text?: string;
  renderTextAsHeader?: boolean;
  list?: (string | CopyableSection)[];
};
export const copyFormattedVersionToClipboard = async (
  data: CopyableSection[],
  name?: string
) => {
  const htmlContent = data
    .map((section) => generateContent(section))
    .join("\n\n");

  const clipboardItemData = {
    "text/plain": stripHtml(htmlContent),
    "text/html": htmlContent,
  };

  const clipboardItem = new ClipboardItem(clipboardItemData);
  await navigator.clipboard.write([clipboardItem]);

  if (name) toast.success(`Copied ${name} to clipboard!`);
  else toast.success("Copied to clipboard!");
};

function generateContent(section: CopyableSection, isChild?: boolean) {
  let content = "";
  if (section.text) {
    // If rendering as a header, use h2 for outer sections, h3 for inner ones
    const tag = section.renderTextAsHeader
      ? isChild
        ? "h3"
        : "h2"
      : undefined;

    if (tag) {
      content += `<${tag}>${section.text}</${tag}>`;
    } else {
      content += section.text;
    }
    content += "\n\n";
  }

  if (!section.list || section.list.length === 0) {
    return content;
  }

  content += "<ul>";
  for (const subsection of section.list) {
    content += "<li>";
    const subsectionToRender =
      typeof subsection === "string"
        ? { text: subsection, renderTextAsHeader: false }
        : subsection;
    content += generateContent(subsectionToRender, true).trimEnd();
    content += "</li>\n";
  }
  content += "</ul>";
  return content;
}

type CopyableTableData = {
  title: string;
  description?: string;
  data: string[][];
};
export const copyFormattedTableToClipboard = async (
  data: CopyableTableData,
  section?: string
) => {
  let htmlContent = "";
  htmlContent += `<h2>${data.title}</h2>\n`;
  if (data.description) {
    htmlContent += `<p>${data.description}</p>\n`;
  }
  htmlContent += `<table style="border-collapse: collapse"><tbody>`;
  htmlContent += data.data
    .map(
      (row) =>
        `<tr>${row.map((cell) => `<td style="border: 1px solid #ccc; padding: 5px;">${cell}</td>`).join("   ")}</tr>`
    )
    .join("\n");

  const clipboardItemData = {
    "text/plain": stripHtml(htmlContent),
    "text/html": htmlContent,
  };

  const clipboardItem = new ClipboardItem(clipboardItemData);
  await navigator.clipboard.write([clipboardItem]);

  if (section) toast.success(`Copied ${section} to clipboard!`);
  else toast.success("Copied to clipboard!");
};

export const copyFromMarkdownToClipboard = async (
  data: string,
  section?: string
) => {
  const htmlContent = await marked.parse(data);

  const clipboardItemData = {
    "text/plain": stripHtml(htmlContent),
    "text/html": htmlContent,
  };

  const clipboardItem = new ClipboardItem(clipboardItemData);
  await navigator.clipboard.write([clipboardItem]);

  if (section) toast.success(`Copied ${section} to clipboard!`);
  else toast.success("Copied to clipboard!");
};
