import resolveConfig from "tailwindcss/resolveConfig.js";
import tailwindConfig from "../../tailwind.config";
import { useHydrated } from "./hydration";
import { useEffect, useMemo, useState } from "react";

// Constants
const TAILWIND_CONFIG = resolveConfig(tailwindConfig);
const SCREENS = TAILWIND_CONFIG.theme.screens;
type Breakpoint = keyof typeof SCREENS;

// Exports
export const useTailwindBreakpoints = () => {
  const isHydrated = useHydrated();
  const [windowWidth, setWindowWidth] = useState<number>();

  // Parse Tailwind theme breakpoints (aka screen) widths
  const allBreakpoints = useMemo(
    () =>
      Object.entries(SCREENS).map(([breakpoint, sizeString]) => ({
        breakpoint: breakpoint as Breakpoint,
        width: Number.parseInt(sizeString, 10),
      })),
    []
  );

  useEffect(() => {
    if (isHydrated) {
      const resizeListener = () => setWindowWidth(window.innerWidth);
      // Set initial window width, before users resize anything
      resizeListener();
      // Subscribe resize listener to keep window width up-to-date
      window.addEventListener("resize", resizeListener);
      // Unsubscribe resize listener when hook deps change
      return () => window.removeEventListener("resize", resizeListener);
    }
  }, [isHydrated]);

  return useMemo(() => {
    // No window width means we haven't hydrated yet, so we start with no matches
    const matchedBreakpoints = new Set<Breakpoint>();
    if (windowWidth) {
      allBreakpoints
        .filter(({ width }) => width <= windowWidth)
        .forEach(({ breakpoint }) => matchedBreakpoints.add(breakpoint));

      // Still match "xs" when window width is less than the "sm" width
      matchedBreakpoints.add("xs");
    } else {
      // We don't have information about this on the server side, but because most of our real usage
      // is on the desktop, we assume that the user is on a desktop.
      //
      // There are better ways to handle SSR and responsive design, but this is good enough for the
      // more common desktop use case we want to support.
      matchedBreakpoints.add("xs");
      matchedBreakpoints.add("sm");
      matchedBreakpoints.add("md");
    }
    return { windowWidth, matchedBreakpoints };
  }, [windowWidth, allBreakpoints]);
};
