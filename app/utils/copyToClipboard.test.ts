import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { toast } from "react-toastify";
import { marked } from "marked";
import {
  copyToClipboard,
  copyFormattedVersionToClipboard,
  copyFormattedTableToClipboard,
  copyFromMarkdownToClipboard,
} from "./copyToClipboard";
import stripHtml from "./stripHtml";
import { waitFor } from "@testing-library/react";

// Mock dependencies
vi.mock("react-toastify", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock("./stripHtml", () => ({
  default: vi.fn(),
}));

vi.mock("marked", () => ({
  marked: {
    parse: vi.fn(),
  },
}));

describe("copyToClipboard", () => {
  let mockWriteText: ReturnType<typeof vi.fn>;
  let mockWrite: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Mock navigator.clipboard
    mockWriteText = vi.fn();
    mockWrite = vi.fn();

    Object.defineProperty(navigator, "clipboard", {
      value: {
        writeText: mockWriteText,
        write: mockWrite,
      },
      writable: true,
    });

    // Mock ClipboardItem
    const MockClipboardItem = vi
      .fn()
      .mockImplementation((data) => ({ data })) as any;
    MockClipboardItem.supports = vi.fn().mockReturnValue(true);
    global.ClipboardItem = MockClipboardItem;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("copyToClipboard", () => {
    it("should copy text to clipboard and show success toast", async () => {
      mockWriteText.mockResolvedValue(undefined);

      copyToClipboard("test text");

      expect(mockWriteText).toHaveBeenCalledWith("test text");

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith("Copied to clipboard!");
      });
    });

    it("should copy text to clipboard and show success toast with section", async () => {
      mockWriteText.mockResolvedValue(undefined);

      copyToClipboard("test text", "notes");

      expect(mockWriteText).toHaveBeenCalledWith("test text");

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith(
          "Copied notes to clipboard!"
        );
      });
    });

    it("should show error toast when clipboard write fails", async () => {
      const error = new Error("Clipboard write failed");
      mockWriteText.mockRejectedValue(error);

      copyToClipboard("test text");

      expect(mockWriteText).toHaveBeenCalledWith("test text");

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Failed to copy text.");
      });
    });
  });

  describe("copyFormattedVersionToClipboard", () => {
    beforeEach(() => {
      vi.mocked(stripHtml).mockReturnValue("stripped html content");
      mockWrite.mockResolvedValue(undefined);
    });

    it("should copy formatted content with simple string list", async () => {
      const data = [
        {
          text: "Test Section",
          renderTextAsHeader: true,
          list: ["item 1", "item 2", "item 3"],
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html":
              "<h2>Test Section</h2>\n\n<ul><li>item 1</li>\n<li>item 2</li>\n<li>item 3</li>\n</ul>",
          },
        }),
      ]);
      expect(toast.success).toHaveBeenCalledWith("Copied to clipboard!");
    });

    it("should copy formatted content with nested sections", async () => {
      const data = [
        {
          text: "Main Section",
          renderTextAsHeader: true,
          list: [
            {
              text: "Sub Section",
              renderTextAsHeader: true,
              list: ["nested item 1", "nested item 2"],
            },
          ],
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html":
              "<h2>Main Section</h2>\n\n<ul><li><h3>Sub Section</h3>\n\n<ul><li>nested item 1</li>\n<li>nested item 2</li>\n</ul></li>\n</ul>",
          },
        }),
      ]);
      expect(toast.success).toHaveBeenCalledWith("Copied to clipboard!");
    });

    it("should copy formatted content with section name in toast", async () => {
      const data = [
        {
          title: "Test Section",
          list: ["item 1"],
        },
      ];

      await copyFormattedVersionToClipboard(data, "meeting notes");

      expect(toast.success).toHaveBeenCalledWith(
        "Copied meeting notes to clipboard!"
      );
    });

    it("should handle sections without title", async () => {
      const data = [
        {
          list: ["item 1", "item 2"],
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html": "<ul><li>item 1</li>\n<li>item 2</li>\n</ul>",
          },
        }),
      ]);
    });

    it("should handle sections without list", async () => {
      const data = [
        {
          text: "Title Only Section",
          renderTextAsHeader: true,
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html": "<h2>Title Only Section</h2>\n\n",
          },
        }),
      ]);
    });

    it("should handle sections with non-title text", async () => {
      const data = [
        {
          text: "Non-title Text",
          renderTextAsHeader: false,
          list: ["item 1", "item 2"],
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html":
              "Non-title Text\n\n<ul><li>item 1</li>\n<li>item 2</li>\n</ul>",
          },
        }),
      ]);
    });

    it("should handle intermixed strings and subsections", async () => {
      const data = [
        {
          text: "Main Section",
          renderTextAsHeader: true,
          list: [
            "simple item",
            {
              text: "Nested Section",
              renderTextAsHeader: true,
              list: [
                "nested item 1",
                {
                  text: "nested item 2",
                  renderTextAsHeader: false,
                  list: [
                    "nested item 2.1",
                    {
                      text: "nested item 2.2",
                      renderTextAsHeader: false,
                    },
                  ],
                },
              ],
            },
            "another simple item",
          ],
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html":
              "<h2>Main Section</h2>\n\n<ul><li>simple item</li>\n<li><h3>Nested Section</h3>\n\n<ul><li>nested item 1</li>\n<li>nested item 2\n\n<ul><li>nested item 2.1</li>\n<li>nested item 2.2</li>\n</ul></li>\n</ul></li>\n<li>another simple item</li>\n</ul>",
          },
        }),
      ]);
    });

    it("should handle multiple top-level sections", async () => {
      const data = [
        {
          text: "First Section",
          renderTextAsHeader: true,
          list: ["item 1", "item 2"],
        },
        {
          text: "Second Section",
          renderTextAsHeader: true,
          list: ["item A", "item B"],
        },
      ];

      await copyFormattedVersionToClipboard(data);

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped html content",
            "text/html":
              "<h2>First Section</h2>\n\n<ul><li>item 1</li>\n<li>item 2</li>\n</ul>\n\n<h2>Second Section</h2>\n\n<ul><li>item A</li>\n<li>item B</li>\n</ul>",
          },
        }),
      ]);
    });
  });

  describe("copyFormattedTableToClipboard", () => {
    beforeEach(() => {
      vi.mocked(stripHtml).mockReturnValue("stripped table content");
      mockWrite.mockResolvedValue(undefined);
    });

    it("should copy formatted table with title and description", async () => {
      const data = {
        title: "Test Table",
        description: "This is a test table",
        data: [
          ["Header 1", "Header 2"],
          ["Row 1 Col 1", "Row 1 Col 2"],
          ["Row 2 Col 1", "Row 2 Col 2"],
        ],
      };

      await copyFormattedTableToClipboard(data);

      const expectedHtml = `<h2>Test Table</h2>\n<p>This is a test table</p>\n<table style="border-collapse: collapse"><tbody><tr><td style="border: 1px solid #ccc; padding: 5px;">Header 1</td>   <td style="border: 1px solid #ccc; padding: 5px;">Header 2</td></tr>\n<tr><td style="border: 1px solid #ccc; padding: 5px;">Row 1 Col 1</td>   <td style="border: 1px solid #ccc; padding: 5px;">Row 1 Col 2</td></tr>\n<tr><td style="border: 1px solid #ccc; padding: 5px;">Row 2 Col 1</td>   <td style="border: 1px solid #ccc; padding: 5px;">Row 2 Col 2</td></tr>`;

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped table content",
            "text/html": expectedHtml,
          },
        }),
      ]);
      expect(toast.success).toHaveBeenCalledWith("Copied to clipboard!");
    });

    it("should copy formatted table without description", async () => {
      const data = {
        title: "Simple Table",
        data: [
          ["A", "B"],
          ["1", "2"],
        ],
      };

      await copyFormattedTableToClipboard(data);

      const expectedHtml = `<h2>Simple Table</h2>\n<table style="border-collapse: collapse"><tbody><tr><td style="border: 1px solid #ccc; padding: 5px;">A</td>   <td style="border: 1px solid #ccc; padding: 5px;">B</td></tr>\n<tr><td style="border: 1px solid #ccc; padding: 5px;">1</td>   <td style="border: 1px solid #ccc; padding: 5px;">2</td></tr>`;

      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped table content",
            "text/html": expectedHtml,
          },
        }),
      ]);
    });

    it("should copy formatted table with section name in toast", async () => {
      const data = {
        title: "Test Table",
        data: [["A", "B"]],
      };

      await copyFormattedTableToClipboard(data, "summary table");

      expect(toast.success).toHaveBeenCalledWith(
        "Copied summary table to clipboard!"
      );
    });
  });

  describe("copyFromMarkdownToClipboard", () => {
    beforeEach(() => {
      vi.mocked(stripHtml).mockReturnValue("stripped markdown content");
      vi.mocked(marked.parse).mockResolvedValue("<p>Parsed markdown</p>");
      mockWrite.mockResolvedValue(undefined);
    });

    it("should copy markdown content converted to HTML", async () => {
      const markdownData = "# Heading\n\nThis is **bold** text.";

      await copyFromMarkdownToClipboard(markdownData);

      expect(marked.parse).toHaveBeenCalledWith(markdownData);
      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped markdown content",
            "text/html": "<p>Parsed markdown</p>",
          },
        }),
      ]);
      expect(toast.success).toHaveBeenCalledWith("Copied to clipboard!");
    });

    it("should copy markdown content with section name in toast", async () => {
      const markdownData = "# Test\n\nContent";

      await copyFromMarkdownToClipboard(markdownData, "markdown notes");

      expect(toast.success).toHaveBeenCalledWith(
        "Copied markdown notes to clipboard!"
      );
    });

    it("should handle empty markdown content", async () => {
      const markdownData = "";
      vi.mocked(marked.parse).mockResolvedValue("");

      await copyFromMarkdownToClipboard(markdownData);

      expect(marked.parse).toHaveBeenCalledWith("");
      expect(mockWrite).toHaveBeenCalledWith([
        expect.objectContaining({
          data: {
            "text/plain": "stripped markdown content",
            "text/html": "",
          },
        }),
      ]);
    });
  });
});
