export default function isApiError(
  error: unknown
): error is { response: { status: number } } {
  if (typeof error !== "object" || error === null) {
    return false;
  }

  if (!Object.hasOwn(error, "response")) {
    return false;
  }

  const response = (error as { response: unknown }).response;
  if (typeof response !== "object" || response === null) {
    return false;
  }

  if (!Object.hasOwn(response, "status")) {
    return false;
  }

  return typeof (response as { status: unknown }).status === "number";
}
