import { AttendeeOption } from "~/api/attendees/types";
import { AttendeesMultiSelect } from "./AttendeeMultiSelect";
import { render, screen } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { vi } from "vitest";

describe("AttendeesMultiSelect", () => {
  const onChange = vi.fn();

  beforeEach(() => {
    vi.mock("~/context/flags");
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  function expectToBeDefined<T>(value: T | undefined): asserts value is T {
    expect(value).toBeDefined();
  }

  const initialOptions: AttendeeOption[] = [
    { uuid: "1", name: "<PERSON>", type: "client" },
    { uuid: "2", name: "<PERSON>", type: "user" },
  ];

  const selectedOptions: AttendeeOption[] = [
    { uuid: "1", name: "<PERSON>", type: "client" },
  ];

  it("renders the component with no selected options", () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={[]}
        onChange={onChange}
      />
    );

    expect(screen.getByText("Select an item")).toBeInTheDocument();
  });

  it("renders the component with selected options", () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={selectedOptions}
        onChange={onChange}
      />
    );

    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.queryByText("Jane Smith")).not.toBeInTheDocument();
  });

  it("opens the popover when the button is clicked", async () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={selectedOptions}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));

    expect(screen.getByPlaceholderText("Search...")).toBeInTheDocument();
    expect(screen.getByText("Jane Smith")).toBeInTheDocument();
  });

  it("filters options based on search term", async () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={selectedOptions}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));

    const searchInput = screen.getByPlaceholderText("Search...");
    await user.type(searchInput, "John");

    expect(screen.queryByText("Jane Smith")).not.toBeInTheDocument();
    // John Smith is a selected option, so it's in the document
    expect(screen.getByText("John Doe")).toBeInTheDocument();
  });

  it("adds a new option when 'Add' button is clicked", async () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={selectedOptions}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));

    const searchInput = screen.getByPlaceholderText("Search...");
    await user.type(searchInput, "New Attendee");

    const addButton = screen.getByText('Add "New Attendee"');
    await user.click(addButton);

    expect(onChange).toHaveBeenCalledWith([
      ...selectedOptions,
      expect.objectContaining({ name: "New Attendee" }),
    ]);
  });

  it("toggles option selection", async () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={selectedOptions}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));
    await user.click(screen.getByText("Jane Smith"));

    expect(onChange).toHaveBeenCalledWith([
      ...selectedOptions,
      expect.objectContaining({ name: "Jane Smith" }),
    ]);
  });

  it("removes selected option when clicked again", async () => {
    render(
      <AttendeesMultiSelect
        initialOptions={initialOptions}
        selected={selectedOptions}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));
    const optionButton = screen.queryAllByText("John Doe").pop();
    expectToBeDefined(optionButton);
    await user.click(optionButton);

    expect(onChange).toHaveBeenCalledWith([]);
  });

  it("shows client types when there are multiple client types", async () => {
    const optionsWithClientTypes: AttendeeOption[] = [
      ...initialOptions,
      { uuid: "3", name: "James Brown", type: "client", clientType: "client" },
      { uuid: "4", name: "Joan Blue", type: "client", clientType: "account" },
    ];

    render(
      <AttendeesMultiSelect
        initialOptions={optionsWithClientTypes}
        selected={[]}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));

    expect(screen.getByText("James Brown").nextSibling).toHaveTextContent(
      "client (client)"
    );
    expect(screen.getByText("Joan Blue").nextSibling).toHaveTextContent(
      "client (account)"
    );
  });

  it("does not show client types when all clients have the same type", async () => {
    const optionsWithClientTypes: AttendeeOption[] = [
      { uuid: "3", name: "James Brown", type: "client", clientType: "account" },
      { uuid: "4", name: "Joan Blue", type: "client", clientType: "account" },
    ];

    render(
      <AttendeesMultiSelect
        initialOptions={optionsWithClientTypes}
        selected={[]}
        onChange={onChange}
      />
    );

    const user = userEvent.setup();
    await user.click(screen.getByRole("combobox"));

    expect(screen.getByText("James Brown").nextSibling).toHaveTextContent(
      /^client$/
    );
    expect(screen.getByText("Joan Blue").nextSibling).toHaveTextContent(
      /^client$/
    );
  });
});
