import { Link } from "react-router";
import {
  Calendar,
  GraduationCap,
  LayoutDashboard,
  Mail,
  NotebookText,
  Presentation,
  RefreshCw,
} from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/@shadcn/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { cn } from "~/@shadcn/utils";

const OnboardingMenu = ({
  className,
  isV2,
  isCollapsed,
}: {
  className?: string;
  isV2?: boolean;
  isCollapsed?: boolean;
}) => {
  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              isV2 ? "h-12 w-16" : "h-10 w-12",
              !isCollapsed && "w-full justify-start gap-2 px-4"
            )}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <>
                  <GraduationCap className={isV2 ? "!h-6 !w-6" : "!h-8 !w-8"} />
                  {isV2 && !isCollapsed && <span>Onboarding</span>}
                </>
              </TooltipTrigger>
              <TooltipContent side="right">Onboarding Guides</TooltipContent>
            </Tooltip>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="right"
          align="end"
          hideWhenDetached={true}
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          {guides.map(({ key, title, icon, link }) => (
            <DropdownMenuItem asChild key={key}>
              <Button variant="ghost" asChild className="w-full justify-start">
                <Link to={link}>
                  {icon}
                  <span className="ml-1">{title}</span>
                </Link>
              </Button>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

const guides = [
  {
    key: "advisor-hub-overview",
    title: "Overview of Advisor Hub",
    icon: <LayoutDashboard className="!h-4 !w-4" />,
    link: "/dashboard?onboarding=true",
  },
  {
    key: "notes-overview",
    title: "Overview of Notes",
    icon: <NotebookText className="!h-4 !w-4" />,
    link: "/notes/create/intro_mock_uuid?onboarding=true",
  },
  {
    key: "post-meeting-meeting",
    title: "Post-meeting overview",
    icon: <Presentation className="!h-4 !w-4" />,
    link: "/notes/intro_mock_uuid?onboarding=true&onboarding-section=overview",
  },
  {
    key: "sync-to-crm",
    title: "How to sync to CRM?",
    icon: <RefreshCw className="!h-4 !w-4" />,
    link: "/notes/intro_mock_uuid?onboarding=true&onboarding-section=sync-to-crm",
  },
  {
    key: "follow-up-email",
    title: "How to preview & send follow-up emails?",
    icon: <Mail className="!h-4 !w-4" />,
    link: "/notes/intro_mock_uuid?onboarding=true&onboarding-section=follow-up-email",
  },
  {
    key: "integrate-calendar",
    title: "How to integrate my calendar?",
    icon: <Calendar className="!h-4 !w-4" />,
    link: "/settings/integrations?onboarding=true",
  },
];

export default OnboardingMenu;
