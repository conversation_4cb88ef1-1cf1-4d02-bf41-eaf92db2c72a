import { useState } from "react";
import { Link, NavLink } from "react-router";
import {
  ChartNoAxesCombined,
  House,
  ListChecks,
  LogOut,
  NotepadText,
  PanelLeftClose,
  PanelRightClose,
  Settings,
  Users,
} from "lucide-react";
import { cva } from "class-variance-authority";

import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { useFlag } from "~/context/flags";
import { CompanyIcon } from "~/@ui/assets/CompanyIcon";
import OnboardingMenu from "./OnboardingMenu";
import { cn } from "~/@shadcn/utils";
import { saveUiSettingsInCookies } from "~/utils/uiCookies";
import { useUiSettings } from "~/context/uiSettings";

const NavBar = () => {
  const uiSettings = useUiSettings();
  const [isCollapsed, setIsCollapsed] = useState(uiSettings.navbarCollapsed);

  const isClientViewEnabled = !!useFlag("EnableClientView");
  const isInsightsDashboardEnabled = !!useFlag(
    "EnablePracticeInsightsDashboard"
  );
  const isOnboardingEnabled = !!useFlag("EnableInAppTutorials");
  const isUnifyAdvisorHubAndNotesEnabled = !!useFlag(
    "EnableUnifyAdvisorHubAndNotes"
  );

  const primaryMenuItems = getPrimaryMenuItems(
    isClientViewEnabled,
    isInsightsDashboardEnabled,
    isUnifyAdvisorHubAndNotesEnabled
  );
  const secondaryMenuItems = getSecondaryMenuItems();

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
    saveUiSettingsInCookies({
      ...uiSettings,
      navbarCollapsed: !isCollapsed,
    });
  };

  return (
    <nav
      className={cn(
        "hidden flex-col bg-muted py-2 md:flex",
        isCollapsed ? "w-16 items-center" : "w-44"
      )}
    >
      {/* logo */}
      {isCollapsed ? (
        <Link to="/" className="flex items-center gap-2">
          <CompanyIcon className="h-10 w-10" />
        </Link>
      ) : (
        <span className="flex items-center justify-between">
          <Link to="/" className="flex items-center gap-2">
            <CompanyIcon className="ml-4 h-10 w-8 p-0" />
          </Link>

          <PanelLeftClose
            className="mr-4 cursor-pointer text-muted-foreground"
            onClick={toggleSidebar}
          />
        </span>
      )}

      <div className="mt-6 flex flex-col gap-2" id="LayoutNavBar-upper">
        {primaryMenuItems.map(({ label, path, tooltip, icon: Icon }) => {
          if (isCollapsed) {
            return (
              <Tooltip key={label}>
                <TooltipTrigger asChild>
                  <NavLink
                    to={path}
                    className={navLinkVariants({ variant: "desktop-primary" })}
                  >
                    <Icon />
                  </NavLink>
                </TooltipTrigger>
                <TooltipContent side="right">{tooltip}</TooltipContent>
              </Tooltip>
            );
          }

          return (
            <NavLink
              to={path}
              key={label}
              className={cn(
                `${navLinkVariants({ variant: "desktop-primary" })}`,
                "w-full justify-start gap-2 px-4"
              )}
            >
              <Icon />
              <span>{label}</span>
            </NavLink>
          );
        })}
      </div>

      <div className="mt-auto flex flex-col gap-2" data-onboarding="usermenu">
        {isOnboardingEnabled && (
          <OnboardingMenu isV2 isCollapsed={isCollapsed} />
        )}
        {secondaryMenuItems.map(
          ({ label, path, tooltip, icon: Icon, iconClass, labelClass }) => {
            if (isCollapsed) {
              return (
                <Tooltip key={label}>
                  <TooltipTrigger asChild>
                    <NavLink
                      to={path}
                      className={navLinkVariants({
                        variant: "desktop-secondary",
                      })}
                    >
                      <Icon className={`${iconClass} shrink-0`} />
                    </NavLink>
                  </TooltipTrigger>
                  <TooltipContent side="right">{tooltip}</TooltipContent>
                </Tooltip>
              );
            }

            return (
              <NavLink
                to={path}
                key={label}
                className={cn(
                  `${navLinkVariants({ variant: "desktop-secondary" })}`,
                  "w-full justify-start gap-2 px-4"
                )}
              >
                <Icon className={`${iconClass} shrink-0`} />
                <span className={labelClass}>{label}</span>
              </NavLink>
            );
          }
        )}

        {isCollapsed && (
          <PanelRightClose
            onClick={toggleSidebar}
            className="h-12 w-16 cursor-pointer px-5 text-muted-foreground"
          />
        )}
      </div>
    </nav>
  );
};

const navLinkVariants = cva(
  "inline-flex shrink-0 select-none items-center justify-center px-3 hover:bg-card [&.active]:bg-card [&.active]:text-card-foreground",
  {
    variants: {
      variant: {
        "desktop-primary": "h-12 w-16",
        "desktop-secondary": "h-12 w-16",
      },
    },
  }
);

function getPrimaryMenuItems(
  isClientViewEnabled: boolean,
  isInsightsDashboardEnabled: boolean,
  isUnifyAdvisorHubAndNotesEnabled: boolean
) {
  return [
    {
      label: "Hub",
      path: "/dashboard",
      tooltip: "Advisor Hub",
      icon: House,
    },
    ...(isUnifyAdvisorHubAndNotesEnabled
      ? []
      : [
          {
            label: "Notes",
            path: "/notes",
            tooltip: "Notes",
            icon: NotepadText,
          },
        ]),
    {
      label: "Tasks",
      path: "/tasks",
      tooltip: "Tasks",
      icon: ListChecks,
    },
    ...(isClientViewEnabled
      ? [
          {
            label: "Clients",
            path: "/clients",
            tooltip: "Clients",
            icon: Users,
          },
        ]
      : []),
    ...(isInsightsDashboardEnabled
      ? [
          {
            label: "Insights",
            path: "/insights",
            tooltip: "Insights",
            icon: ChartNoAxesCombined,
          },
        ]
      : []),
  ];
}

function getSecondaryMenuItems() {
  return [
    {
      label: "Settings",
      path: "/settings",
      tooltip: "Settings",
      icon: Settings,
    },
    {
      label: "Logout",
      path: "/auth/logout",
      tooltip: "Logout",
      icon: LogOut,
      iconClass: "text-destructive",
      labelClass: "text-destructive",
    },
  ];
}

export default NavBar;
