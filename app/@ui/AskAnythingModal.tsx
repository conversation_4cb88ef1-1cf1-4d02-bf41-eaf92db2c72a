import React from "react";
import { Input } from "~/@shadcn/ui/input";
import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "~/@shadcn/ui/dialog";
import { ScrollArea } from "~/@shadcn/ui/scroll-area";
import { Typography } from "~/@ui/Typography";
import { SummarySection } from "~/api/openapi/generated";
import { Copy, Plus, Replace, Search, Send, Sparkles } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "~/@shadcn/ui/sheet";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { useFlag } from "~/context/flags";

export enum AnswerStatus {
  IDLE = "idle",
  IN_PROCESS = "in process",
  SUCCESS = "success",
  FAILED = "failed",
}

type AskAnythingModalProps = {
  closeModal: (open: boolean) => void;
  answerStatus: AnswerStatus;
  query: string;
  answer: SummarySection | null;
  handleKeyDown: (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  onInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  handleAsk: () => void;
  setQuery: (query: string) => void;
  showAskInput: () => void;
  handleCopy?: () => void;
  addSummaryTopic?: () => void;
  replaceSummaryTopic?: () => void;
  showReplaceCta?: boolean;
};

const LoadingIndicator = () => (
  <div className="flex items-center space-x-2">
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500"></div>
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500 delay-200"></div>
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500 delay-500"></div>
  </div>
);

const AskAnythingModal = ({
  closeModal,
  answerStatus,
  query,
  answer,
  handleKeyDown,
  onInputChange,
  handleAsk,
  handleCopy,
  showAskInput,
  addSummaryTopic,
  replaceSummaryTopic,
  showReplaceCta,
}: AskAnythingModalProps) => {
  const isNewUiEnabled = !!useFlag("EnableNewAskAnythingInterface");

  const ctaList = getCtaList({
    answerStatus,
    addSummaryTopic,
    replaceSummaryTopic,
    showReplaceCta,
    handleCopy,
    showAskInput,
    handleAsk,
    isNewUiEnabled,
    query,
  });

  if (isNewUiEnabled) {
    return (
      <Sheet open onOpenChange={closeModal}>
        {/* NOTE: The abnormally high z-index value is to ensure that this sheet is always on top of Intercom */}
        <SheetContent className="z-[9876543210] flex w-full flex-col sm:w-2/5 sm:max-w-none">
          <SheetHeader>
            <SheetTitle>
              {answerStatus === AnswerStatus.IDLE
                ? "Ask Anything about this Note"
                : "Ask Anything Results"}
            </SheetTitle>
            <SheetDescription />
          </SheetHeader>

          <div className="flex grow flex-col justify-start gap-4 overflow-auto">
            {/* show the question asked by the user (once submitted) */}
            {answerStatus !== AnswerStatus.IDLE && query.trim() && (
              <div className="max-w-[80%] self-end rounded-lg bg-muted p-2 text-sm">
                {query}
              </div>
            )}

            {/* loading indicator */}
            {answerStatus === AnswerStatus.IN_PROCESS && (
              <div className="flex items-baseline gap-1">
                <span className="text-sm">Your answer is on its way</span>
                <div className="scale-75 opacity-75">
                  <LoadingIndicator />
                </div>
              </div>
            )}

            {/* server error */}
            {answerStatus === AnswerStatus.FAILED && (
              <div>
                <Typography variant="body2">
                  I am unable to answer that question.
                  <br />
                  Could you try rephrasing it?
                </Typography>
              </div>
            )}

            {/* show the answer from the server */}
            {answerStatus === AnswerStatus.SUCCESS && answer && (
              <div className="bg-muted_ p-2_ w-full rounded-lg text-sm">
                <Typography variant="h4">{answer.topic}</Typography>
                <ul className="mt-2 flex flex-col gap-2 text-sm">
                  {answer.bullets.map((bullet, index) => (
                    <li key={index} className="ml-4 list-disc">
                      {bullet}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {answerStatus === AnswerStatus.IDLE && (
              <div className="mt-auto">
                <TextareaGrowable
                  value={query}
                  placeholder="Type in your query and press ENTER ⏎ to ask…"
                  onChange={onInputChange}
                  onKeyDown={handleKeyDown}
                  className="!min-h-32 rounded-sm border-none bg-muted p-2 text-sm"
                />
              </div>
            )}
          </div>

          <SheetFooter className="flex shrink-0 flex-row flex-wrap justify-end gap-1">
            {ctaList.map(({ label, onClick, variant, Icon, disabled }) => (
              <Button
                key={label}
                onClick={onClick}
                variant={variant as "outline" | "default"}
                className="hover:bg-primary hover:text-white"
                disabled={disabled}
              >
                <Icon />
                {label}
              </Button>
            ))}
          </SheetFooter>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open onOpenChange={closeModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {answerStatus === AnswerStatus.IDLE
              ? "Ask Anything about this Note"
              : "Ask Anything Results"}
          </DialogTitle>
        </DialogHeader>
        {answerStatus !== AnswerStatus.IDLE && query && (
          <Typography variant="body2">"{query}"</Typography>
        )}
        {answerStatus === AnswerStatus.IDLE ? (
          <div className="flex-row">
            <Input
              className="h-9 rounded-sm"
              value={query}
              onChange={onInputChange}
              onKeyDown={handleKeyDown}
              leftIcon={<Search className="!h-5 !w-5" />}
            />
            <div className="mt-2 flex justify-end">
              <Button
                className="rounded-sm hover:bg-primary hover:text-white"
                disabled={query === ""}
                onClick={handleAsk}
                size="sm"
              >
                Ask
              </Button>
            </div>
          </div>
        ) : (
          <div>
            {answerStatus === AnswerStatus.IN_PROCESS && (
              <div className="flex items-center space-x-2">
                <LoadingIndicator />
                <Typography variant="body2" className="pr-1">
                  Your answer is on its way...
                </Typography>
              </div>
            )}
            {answerStatus === AnswerStatus.FAILED && (
              <div>
                <Typography variant="body2">
                  I am unable to answer that question.
                </Typography>
                <Typography variant="body2">
                  Try rephrasing your question.
                </Typography>
              </div>
            )}
            {answerStatus === AnswerStatus.SUCCESS && answer && (
              <div>
                <Typography variant="h4">{answer.topic}</Typography>
                <ScrollArea className="h-[200px]">
                  <ul className="mt-2 flex flex-col gap-2 text-sm">
                    {answer.bullets.map((bullet, index) => (
                      <li key={index} className="ml-4 list-disc">
                        {bullet}
                      </li>
                    ))}
                  </ul>
                </ScrollArea>
              </div>
            )}
          </div>
        )}
        <DialogFooter>
          <div className="flex w-full flex-wrap justify-end gap-1 pt-3 lg:flex-nowrap">
            {ctaList.map(({ label, onClick, variant, Icon }) => (
              <Button
                key={label}
                onClick={onClick}
                variant={variant as "outline" | "default"}
                className="hover:bg-primary hover:text-white"
              >
                <Icon />
                {label}
              </Button>
            ))}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

type GetCtaListInput = {
  answerStatus: AnswerStatus;
  addSummaryTopic?: () => void;
  replaceSummaryTopic?: () => void;
  showReplaceCta?: boolean;
  handleCopy?: () => void;
  showAskInput?: () => void;
  handleAsk?: () => void;
  isNewUiEnabled: boolean;
  query: string;
};

type CtaType = {
  label: string;
  onClick: (() => void) | undefined;
  Icon: React.ElementType;
  variant: "outline" | "default";
  disabled?: boolean;
};

function getCtaList({
  answerStatus,
  addSummaryTopic,
  replaceSummaryTopic,
  showReplaceCta,
  handleCopy,
  showAskInput,
  handleAsk,
  isNewUiEnabled,
  query,
}: GetCtaListInput): CtaType[] {
  // Show only "Ask" CTA if new UI is enabled and status is idle
  if (isNewUiEnabled && answerStatus === AnswerStatus.IDLE) {
    return [
      {
        label: "Ask",
        onClick: handleAsk,
        Icon: Send,
        variant: "default",
        disabled: !query.trim(),
      },
    ];
  }

  const ctaList: CtaType[] = [];

  // Show most CTAs only when answer is available
  if (answerStatus === AnswerStatus.SUCCESS) {
    if (addSummaryTopic) {
      ctaList.push({
        label: "Add to Summary",
        onClick: addSummaryTopic,
        Icon: Plus,
        variant: "outline",
      });
    }

    if (showReplaceCta && replaceSummaryTopic) {
      ctaList.push({
        label: "Replace section",
        onClick: replaceSummaryTopic,
        Icon: Replace,
        variant: "default",
      });
    }

    if (handleCopy) {
      ctaList.push({
        label: "Copy Answer",
        onClick: handleCopy,
        Icon: Copy,
        variant: "outline",
      });
    }
  }

  // Show "Ask" CTA only when "Replace" CTA doesn't appear and answer has been resolved
  if (
    !showReplaceCta &&
    [AnswerStatus.SUCCESS, AnswerStatus.FAILED].includes(answerStatus)
  ) {
    ctaList.push({
      label: "Ask a Question",
      onClick: showAskInput,
      Icon: Sparkles,
      variant: "default",
    });
  }

  return ctaList;
}

export default AskAnythingModal;
