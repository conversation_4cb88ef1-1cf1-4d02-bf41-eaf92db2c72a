import { useState, useEffect, useCallback } from "react";
import { Search } from "lucide-react";

import { Input } from "~/@shadcn/ui/input";
import { Button } from "~/@shadcn/ui/button";
import { useFetcher } from "react-router";
import { Typography } from "~/@ui/Typography";
import { SummarySection } from "~/api/openapi/generated";
import AskAnythingModal, { AnswerStatus } from "~/@ui/AskAnythingModal";
import { copyFormattedVersionToClipboard } from "~/utils/copyToClipboard";
import { cn } from "~/@shadcn/utils";

type FetcherData = {
  actionType?: string;
  success?: boolean;
  error?: string;
  answer?: SummarySection;
  searchQueryId?: string;
  sectionIndex?: number;
};

const AskMeAnythingForClient = ({
  clientId,
  query,
  setQuery,
  triggerSearch,
  setTriggerSearch,
  className,
}: {
  clientId: string;
  query: string;
  setQuery: (query: string) => void;
  triggerSearch: boolean;
  setTriggerSearch: (triggerSearch: boolean) => void;
  className?: string;
}) => {
  const fetcher = useFetcher<FetcherData>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [answerStatus, setAnswerStatus] = useState<AnswerStatus>(
    AnswerStatus.IDLE
  );
  const [answer, setAnswer] = useState<SummarySection | null>(null);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);
  const openToggle = (status: boolean) => {
    if (status) {
      setQuery("");
    } else {
      setQuery("");
      closeModal();
    }
  };

  useEffect(() => {
    if (fetcher.state !== "idle" || !fetcher.data) {
      return;
    }
    if (fetcher.data.actionType === "search-client") {
      if (fetcher.data.success && fetcher.data?.answer) {
        setAnswerStatus(AnswerStatus.SUCCESS);
        setAnswer(fetcher.data.answer);
      } else {
        setAnswerStatus(AnswerStatus.FAILED);
      }
    }
  }, [fetcher.data, fetcher.state]);

  const handleClick = useCallback(() => {
    setAnswer(null);
    setTriggerSearch(false);
    const formData = new FormData();
    formData.append("actionType", "search-client");
    formData.append("query", query);

    setAnswerStatus(AnswerStatus.IN_PROCESS);
    fetcher.submit(formData, {
      method: "post",
      action: `/clients/${clientId}`,
      encType: "multipart/form-data",
    });
  }, [setTriggerSearch, query, fetcher, clientId]);

  useEffect(() => {
    if (query && triggerSearch) {
      openModal();
      handleClick();
    }
  }, [handleClick, query, triggerSearch]);

  const handleKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      // ignore if query is empty
      if (!query) {
        return;
      }

      event.preventDefault();
      openModal();
      handleClick();
    }
  };

  const handleCopy = () => {
    if (answer) {
      copyFormattedVersionToClipboard(
        [
          {
            text: answer.topic,
            renderTextAsHeader: true,
            list: answer.bullets,
          },
        ],
        "answer"
      );
    }
  };

  return (
    <>
      <div className={cn("flex w-full items-center space-x-2", className)}>
        <Typography variant="body2" className="line-clamp-1 w-full text-right">
          Ask Anything about this Client
        </Typography>
        <div>
          <Input
            className="w-30 h-9 rounded-sm"
            value={query}
            onChange={(e) => {
              setAnswerStatus(AnswerStatus.IDLE);
              setQuery(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            leftIcon={<Search className="!h-5 !w-5" />}
          />
        </div>
        <Button
          className="rounded-sm hover:bg-primary hover:text-white"
          disabled={query === ""}
          onClick={() => {
            openModal();
            handleClick();
          }}
          size="sm"
          variant="outline"
        >
          Ask
        </Button>
      </div>

      {isModalOpen && (
        <AskAnythingModal
          closeModal={() => setIsModalOpen(false)}
          answerStatus={answerStatus}
          query={query}
          answer={answer}
          handleKeyDown={handleKeyDown}
          onInputChange={(e) => {
            setAnswerStatus(AnswerStatus.IDLE);
            setQuery(e.target.value);
          }}
          handleAsk={() => {
            openModal();
            handleClick();
          }}
          setQuery={setQuery}
          showAskInput={() => {
            setAnswerStatus(AnswerStatus.IDLE);
            setQuery("");
          }}
          handleCopy={handleCopy}
        />
      )}
    </>
  );
};

export default AskMeAnythingForClient;
