import React, { useState, useEffect, ChangeEvent } from "react";

import { Label } from "~/@shadcn/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { useFlag } from "~/context/flags";

interface MicrophoneSelectorProps {
  onDeviceChange: (deviceId: string) => void;
  selectedDevice: string;
  disabled?: boolean;
}

const MicrophoneSelector: React.FC<MicrophoneSelectorProps> = ({
  onDeviceChange,
  selectedDevice,
  disabled,
}) => {
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);

  const isMeetingPrepRevampEnabled = useFlag("EnableNewMeetingPrepFlow");

  useEffect(() => {
    const getDevices = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDevices = devices.filter(
        (device) => device.kind === "audioinput"
      );
      setDevices(audioDevices);
      if (audioDevices[0]) {
        isMeetingPrepRevampEnabled
          ? onDeviceChange(audioDevices[0].deviceId || "default")
          : onDeviceChange(audioDevices[0].deviceId);
      }
    };
    getDevices();
  }, [onDeviceChange, isMeetingPrepRevampEnabled]);

  const handleChange = (event: ChangeEvent<HTMLSelectElement>) => {
    onDeviceChange(event.target.value);
  };

  if (isMeetingPrepRevampEnabled) {
    return (
      <div className="w-full">
        <Label>Select Microphone</Label>
        <Select
          value={selectedDevice}
          onValueChange={onDeviceChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-fit max-w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {devices.map((device) => (
              <SelectItem
                key={device.deviceId}
                // NOTE:
                // default microphone can have empty device ID, but `SelectItem` always needs a non-empty value;
                // hence value set to `device.deviceId || "default"`
                value={device.deviceId || "default"}
              >
                {device.label || `Microphone ${device.deviceId}`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  return (
    <div className="w-full">
      <label htmlFor="microphone">Select Microphone: </label>
      <select
        id="microphone"
        value={selectedDevice}
        onChange={handleChange}
        disabled={disabled}
      >
        {devices.map((device) => (
          <option key={device.deviceId} value={device.deviceId}>
            {device.label || `Microphone ${device.deviceId}`}
          </option>
        ))}
      </select>
    </div>
  );
};

export default MicrophoneSelector;
