import * as React from "react";
import { Badge } from "~/@shadcn/ui/badge";
import { Button } from "~/@shadcn/ui/button";
import {
  Command,
  CommandInput,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "~/@shadcn/ui/command";
import { Popover, PopoverTrigger, PopoverContent } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { CheckIcon, CaretSortIcon } from "@radix-ui/react-icons";
import { X } from "lucide-react";
import { Typography } from "~/@ui/Typography";
import { OptionType } from "~/@ui/Combobox";
import { FixedSizeList } from "react-window";
import { FuzzyMatches } from "@nozbe/microfuzz";
import { useFuzzySearchList, Highlight } from "@nozbe/microfuzz/react";
import { useDebounce } from "~/utils/useDebounce";

// Exports
type Props = {
  options: OptionType[];
  selected: string[];
  placeholder?: string;
  onChange: React.Dispatch<React.SetStateAction<string[]>>;
  commandClassName?: string;
  triggerClassName?: string;
  leftIcon?: React.ReactNode;
  itemSize?: number;
  maxHeightPx?: number;
};
/**
 * A version of the MultiSelect component that uses virtualization for better performance with large datasets.
 *
 * Heavily modified version of the MultiSelect component from this PR: https://github.com/shadcn-ui/ui/issues/66
 */
export const VirtualizedMultiSelect = ({
  options,
  selected,
  placeholder = "Select an item",
  onChange,
  commandClassName,
  triggerClassName,
  leftIcon,
  itemSize = 40,
  maxHeightPx = 400,
  ...props
}: Props) => {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  const debouncedSearchTerm = useDebounce(searchQuery, 200);

  const mapResultItem = React.useCallback(
    ({
      item,
      score,
      matches: [highlightRanges],
    }: {
      item: OptionType;
      score: number;
      matches: FuzzyMatches;
    }) => ({
      item,
      highlightRanges,
    }),
    []
  );
  const filteredOptions = useFuzzySearchList({
    list: options,
    queryText: debouncedSearchTerm,
    key: "label",
    mapResultItem,
  });

  const OptionRow = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const option = filteredOptions[index];
    if (!option) return null;
    const optionItem = option.item;

    return (
      <div style={style}>
        <CommandItem
          key={optionItem.value}
          className="gap-2 overflow-hidden text-ellipsis whitespace-nowrap"
          value={optionItem.value}
          onMouseDown={(e) => e.stopPropagation()}
          onSelect={(currentValue) => {
            onChange(
              selected.includes(currentValue)
                ? selected.filter((item) => item !== currentValue)
                : [...selected, currentValue]
            );
            setOpen(true);
          }}
        >
          {option.item.leftIcon ?? <span className="h-4 w-4 shrink-0" />}
          <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
            <Highlight
              ranges={option.highlightRanges ?? null}
              text={optionItem.label}
            />
          </span>
          <CheckIcon
            className={cn(
              "ml-auto h-4 w-4 shrink-0",
              selected.includes(optionItem.value) ? "opacity-100" : "opacity-0"
            )}
          />
        </CommandItem>
      </div>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen} {...props}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between rounded border-gray-300 p-2 text-base hover:border-gray-300 hover:bg-white",
            "h-fit min-h-fit",
            triggerClassName
          )}
          onClick={() => setOpen(!open)}
        >
          {leftIcon}
          {(() => {
            if (selected.length === 0) {
              return (
                <Typography
                  className="inline-flex grow"
                  color="secondary"
                  asChild
                >
                  <span>{placeholder}</span>
                </Typography>
              );
            }
            return (
              <span className="inline-flex grow flex-wrap gap-2">
                {selected.map((item) => {
                  const selectedOption = options.find(
                    ({ value }) => value === item
                  );
                  if (!selectedOption) return null;
                  return (
                    <Badge
                      className="max-w-40 gap-1 [&>svg]:shrink-0"
                      inline="inline"
                      variant="secondary"
                      key={item}
                    >
                      {selectedOption.leftIcon}
                      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                        {selectedOption.label}
                      </span>
                      <Button
                        className="h-5 w-5 [&>svg]:h-4 [&>svg]:w-4"
                        size="icon-xs"
                        variant="ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onChange(selected.filter((i) => i !== item));
                        }}
                        asChild
                      >
                        <span role="button">
                          <X />
                        </span>
                      </Button>
                    </Badge>
                  );
                })}
              </span>
            );
          })()}
          <CaretSortIcon className="h-6 w-6 shrink-0 self-baseline" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit min-w-[var(--radix-popper-anchor-width)] p-0">
        <Command className={commandClassName} shouldFilter={false}>
          <CommandInput
            placeholder="Search..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList className="max-h-fit">
            <CommandEmpty>No item found.</CommandEmpty>
            <CommandGroup>
              <FixedSizeList
                height={Math.min(
                  filteredOptions.length * itemSize,
                  maxHeightPx
                )}
                width="100%"
                itemCount={filteredOptions.length}
                itemSize={itemSize}
              >
                {OptionRow}
              </FixedSizeList>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
